<?php

if (!function_exists('setting')) {
    /**
     * Get a setting value by key.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        static $settings = null;
        
        if ($settings === null) {
            $settings = \App\Models\SiteSetting::all()->pluck('value', 'key');
        }
        
        return $settings->get($key, $default);
    }
}

if (!function_exists('settings')) {
    /**
     * Get all settings as a collection.
     *
     * @return \Illuminate\Support\Collection
     */
    function settings()
    {
        static $settings = null;
        
        if ($settings === null) {
            $settings = \App\Models\SiteSetting::all()->pluck('value', 'key');
        }
        
        return $settings;
    }
}

if (!function_exists('site_name')) {
    /**
     * Get the site name.
     *
     * @return string
     */
    function site_name()
    {
        return setting('site_name', config('app.name', 'Global Ventures Tanzania'));
    }
}

if (!function_exists('site_logo')) {
    /**
     * Get the site logo URL.
     *
     * @return string
     */
    function site_logo()
    {
        $logo = setting('site_logo');
        return $logo ? \Storage::url($logo) : null;
    }
}

if (!function_exists('contact_email')) {
    /**
     * Get the contact email.
     *
     * @return string
     */
    function contact_email()
    {
        return setting('contact_email', '<EMAIL>');
    }
}

if (!function_exists('contact_phone')) {
    /**
     * Get the contact phone.
     *
     * @return string
     */
    function contact_phone()
    {
        return setting('contact_phone', '+255 XXX XXX XXX');
    }
}

if (!function_exists('social_links')) {
    /**
     * Get all social media links.
     *
     * @return array
     */
    function social_links()
    {
        return [
            'facebook' => setting('social_facebook'),
            'twitter' => setting('social_twitter'),
            'instagram' => setting('social_instagram'),
            'linkedin' => setting('social_linkedin'),
            'youtube' => setting('social_youtube'),
            'tiktok' => setting('social_tiktok'),
        ];
    }
}

if (!function_exists('is_feature_enabled')) {
    /**
     * Check if a feature is enabled.
     *
     * @param string $feature
     * @return bool
     */
    function is_feature_enabled($feature)
    {
        return (bool) setting("enable_{$feature}", true);
    }
}

if (!function_exists('is_maintenance_mode')) {
    /**
     * Check if maintenance mode is enabled.
     *
     * @return bool
     */
    function is_maintenance_mode()
    {
        return (bool) setting('maintenance_mode', false);
    }
}

if (!function_exists('maintenance_message')) {
    /**
     * Get the maintenance mode message.
     *
     * @return string
     */
    function maintenance_message()
    {
        return setting('maintenance_message', 'We are currently performing maintenance. Please check back soon.');
    }
}
