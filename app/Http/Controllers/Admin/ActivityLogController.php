<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminActivityLog;
use App\Models\User;
use Illuminate\Http\Request;

class ActivityLogController extends Controller
{
    /**
     * Display a listing of activity logs.
     */
    public function index(Request $request)
    {
        if (!auth()->user()->hasPermission('view_logs')) {
            abort(403, 'You do not have permission to view activity logs.');
        }

        $logs = AdminActivityLog::with('user')
            ->when($request->search, function ($query, $search) {
                $query->where('description', 'like', "%{$search}%")
                      ->orWhere('action', 'like', "%{$search}%")
                      ->orWhere('model_type', 'like', "%{$search}%");
            })
            ->when($request->user_id, function ($query, $userId) {
                $query->where('user_id', $userId);
            })
            ->when($request->action, function ($query, $action) {
                $query->where('action', $action);
            })
            ->when($request->model_type, function ($query, $modelType) {
                $query->where('model_type', $modelType);
            })
            ->when($request->date_from, function ($query, $dateFrom) {
                $query->whereDate('created_at', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($query, $dateTo) {
                $query->whereDate('created_at', '<=', $dateTo);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        $users = User::where('is_active', true)->get();
        $actions = AdminActivityLog::distinct()->pluck('action')->filter();
        $modelTypes = AdminActivityLog::distinct()->pluck('model_type')->filter();

        return view('admin.activity-logs.index', compact('logs', 'users', 'actions', 'modelTypes'));
    }

    /**
     * Display the specified activity log.
     */
    public function show(AdminActivityLog $log)
    {
        if (!auth()->user()->hasPermission('view_logs')) {
            abort(403, 'You do not have permission to view activity logs.');
        }

        $log->load('user');

        return view('admin.activity-logs.show', compact('log'));
    }
}
