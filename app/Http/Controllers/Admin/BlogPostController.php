<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogPostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_blog')) {
            abort(403, 'You do not have permission to view blog posts.');
        }

        $posts = BlogPost::with('author')
            ->when(request('search'), function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                      ->orWhere('content', 'like', "%{$search}%");
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->when(request('category'), function ($query, $category) {
                $query->where('category', $category);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $categories = BlogPost::distinct()->pluck('category')->filter();

        return view('admin.blog-posts.index', compact('posts', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_blog')) {
            abort(403, 'You do not have permission to create blog posts.');
        }

        return view('admin.blog-posts.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_blog')) {
            abort(403, 'You do not have permission to create blog posts.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blog_posts,slug',
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:100',
            'tags' => 'nullable|array',
            'featured_image' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $validated['author_id'] = auth()->id();

        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $post = BlogPost::create($validated);

        AdminActivityLog::log('created', $post, "Created blog post: {$post->title}");

        return redirect()->route('admin.blog-posts.index')
            ->with('success', 'Blog post created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(BlogPost $blogPost)
    {
        if (!auth()->user()->hasPermission('view_blog')) {
            abort(403, 'You do not have permission to view blog posts.');
        }

        $blogPost->load('author');

        return view('admin.blog-posts.show', compact('blogPost'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogPost $blogPost)
    {
        if (!auth()->user()->hasPermission('edit_blog')) {
            abort(403, 'You do not have permission to edit blog posts.');
        }

        return view('admin.blog-posts.edit', compact('blogPost'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, BlogPost $blogPost)
    {
        if (!auth()->user()->hasPermission('edit_blog')) {
            abort(403, 'You do not have permission to edit blog posts.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blog_posts,slug,' . $blogPost->id,
            'content' => 'required|string',
            'excerpt' => 'nullable|string|max:500',
            'category' => 'nullable|string|max:100',
            'tags' => 'nullable|array',
            'featured_image' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        if ($validated['status'] === 'published' && empty($validated['published_at']) && $blogPost->status !== 'published') {
            $validated['published_at'] = now();
        }

        $oldValues = $blogPost->toArray();
        $blogPost->update($validated);

        AdminActivityLog::log('updated', $blogPost, "Updated blog post: {$blogPost->title}", $oldValues, $blogPost->toArray());

        return redirect()->route('admin.blog-posts.index')
            ->with('success', 'Blog post updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlogPost $blogPost)
    {
        if (!auth()->user()->hasPermission('delete_blog')) {
            abort(403, 'You do not have permission to delete blog posts.');
        }

        $title = $blogPost->title;
        $blogPost->delete();

        AdminActivityLog::log('deleted', null, "Deleted blog post: {$title}");

        return redirect()->route('admin.blog-posts.index')
            ->with('success', 'Blog post deleted successfully.');
    }

    /**
     * Publish a blog post.
     */
    public function publish(BlogPost $blogPost)
    {
        if (!auth()->user()->hasPermission('edit_blog')) {
            abort(403, 'You do not have permission to publish blog posts.');
        }

        $blogPost->update([
            'status' => 'published',
            'published_at' => now(),
        ]);

        AdminActivityLog::log('updated', $blogPost, "Published blog post: {$blogPost->title}");

        return response()->json([
            'success' => true,
            'message' => 'Blog post published successfully.'
        ]);
    }

    /**
     * Unpublish a blog post.
     */
    public function unpublish(BlogPost $blogPost)
    {
        if (!auth()->user()->hasPermission('edit_blog')) {
            abort(403, 'You do not have permission to unpublish blog posts.');
        }

        $blogPost->update([
            'status' => 'draft',
        ]);

        AdminActivityLog::log('updated', $blogPost, "Unpublished blog post: {$blogPost->title}");

        return response()->json([
            'success' => true,
            'message' => 'Blog post unpublished successfully.'
        ]);
    }
}
