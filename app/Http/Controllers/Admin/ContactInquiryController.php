<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactInquiry;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;

class ContactInquiryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_inquiries')) {
            abort(403, 'You do not have permission to view contact inquiries.');
        }

        $inquiries = ContactInquiry::with('assignedTo')
            ->when(request('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('subject', 'like', "%{$search}%")
                      ->orWhere('message', 'like', "%{$search}%");
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('status', $status);
            })
            ->when(request('priority'), function ($query, $priority) {
                $query->where('priority', $priority);
            })
            ->when(request('assigned_to'), function ($query, $assignedTo) {
                $query->where('assigned_to', $assignedTo);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $users = \App\Models\User::where('is_active', true)->get();

        return view('admin.inquiries.index', compact('inquiries', 'users'));
    }

    /**
     * Display the specified resource.
     */
    public function show(ContactInquiry $inquiry)
    {
        if (!auth()->user()->hasPermission('view_inquiries')) {
            abort(403, 'You do not have permission to view contact inquiries.');
        }

        $inquiry->load('assignedTo');

        // Mark as read if not already
        if (!$inquiry->is_read) {
            $inquiry->update(['is_read' => true]);
            AdminActivityLog::log('updated', $inquiry, "Marked inquiry as read: {$inquiry->subject}");
        }

        return view('admin.inquiries.show', compact('inquiry'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContactInquiry $inquiry)
    {
        if (!auth()->user()->hasPermission('edit_inquiries')) {
            abort(403, 'You do not have permission to edit contact inquiries.');
        }

        $validated = $request->validate([
            'status' => 'required|in:new,in_progress,resolved,closed',
            'priority' => 'required|in:low,medium,high,urgent',
            'notes' => 'nullable|string',
        ]);

        $oldValues = $inquiry->toArray();
        $inquiry->update($validated);

        AdminActivityLog::log('updated', $inquiry, "Updated inquiry: {$inquiry->subject}", $oldValues, $inquiry->toArray());

        return redirect()->route('admin.inquiries.show', $inquiry)
            ->with('success', 'Inquiry updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContactInquiry $inquiry)
    {
        if (!auth()->user()->hasPermission('delete_inquiries')) {
            abort(403, 'You do not have permission to delete contact inquiries.');
        }

        $subject = $inquiry->subject;
        $inquiry->delete();

        AdminActivityLog::log('deleted', null, "Deleted inquiry: {$subject}");

        return redirect()->route('admin.inquiries.index')
            ->with('success', 'Inquiry deleted successfully.');
    }

    /**
     * Assign inquiry to a user.
     */
    public function assign(Request $request, ContactInquiry $inquiry)
    {
        if (!auth()->user()->hasPermission('edit_inquiries')) {
            abort(403, 'You do not have permission to assign inquiries.');
        }

        $validated = $request->validate([
            'assigned_to' => 'nullable|exists:users,id',
        ]);

        $oldAssignedTo = $inquiry->assigned_to;
        $inquiry->update($validated);

        $assignedUser = $validated['assigned_to'] ? \App\Models\User::find($validated['assigned_to']) : null;
        $message = $assignedUser
            ? "Assigned inquiry to {$assignedUser->name}: {$inquiry->subject}"
            : "Unassigned inquiry: {$inquiry->subject}";

        AdminActivityLog::log('updated', $inquiry, $message);

        return response()->json([
            'success' => true,
            'message' => $assignedUser ? 'Inquiry assigned successfully.' : 'Inquiry unassigned successfully.'
        ]);
    }

    /**
     * Add response to inquiry.
     */
    public function respond(Request $request, ContactInquiry $inquiry)
    {
        if (!auth()->user()->hasPermission('edit_inquiries')) {
            abort(403, 'You do not have permission to respond to inquiries.');
        }

        $validated = $request->validate([
            'response' => 'required|string',
            'status' => 'required|in:new,in_progress,resolved,closed',
        ]);

        $responses = $inquiry->responses ?? [];
        $responses[] = [
            'user_id' => auth()->id(),
            'user_name' => auth()->user()->name,
            'response' => $validated['response'],
            'created_at' => now()->toISOString(),
        ];

        $inquiry->update([
            'responses' => $responses,
            'status' => $validated['status'],
            'responded_at' => now(),
        ]);

        AdminActivityLog::log('updated', $inquiry, "Added response to inquiry: {$inquiry->subject}");

        return redirect()->route('admin.inquiries.show', $inquiry)
            ->with('success', 'Response added successfully.');
    }

    /**
     * Export inquiries to CSV.
     */
    public function exportCsv(Request $request)
    {
        if (!auth()->user()->hasPermission('view_inquiries')) {
            abort(403, 'You do not have permission to export inquiries.');
        }

        $inquiries = ContactInquiry::with('assignedTo')
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->priority, function ($query, $priority) {
                $query->where('priority', $priority);
            })
            ->orderBy('created_at', 'desc')
            ->get();

        $csvData = [];
        $csvData[] = [
            'ID', 'Name', 'Email', 'Phone', 'Subject', 'Message', 'Status', 'Priority',
            'Assigned To', 'Created At', 'Responded At'
        ];

        foreach ($inquiries as $inquiry) {
            $csvData[] = [
                $inquiry->id,
                $inquiry->name,
                $inquiry->email,
                $inquiry->phone,
                $inquiry->subject,
                $inquiry->message,
                ucfirst($inquiry->status),
                ucfirst($inquiry->priority),
                $inquiry->assignedTo ? $inquiry->assignedTo->name : 'Unassigned',
                $inquiry->created_at->format('Y-m-d H:i:s'),
                $inquiry->responded_at ? $inquiry->responded_at->format('Y-m-d H:i:s') : 'Not responded',
            ];
        }

        $filename = 'contact_inquiries_' . now()->format('Y_m_d_H_i_s') . '.csv';

        AdminActivityLog::log('updated', null, 'Exported contact inquiries to CSV');

        return Response::streamDownload(function () use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }
}
