<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\BlogPost;
use App\Models\Service;
use App\Models\Testimonial;
use App\Models\ContactInquiry;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Check permission
        if (!auth()->user()->hasPermission('view_dashboard')) {
            abort(403, 'You do not have permission to view the dashboard.');
        }

        // Get dashboard statistics
        $stats = $this->getDashboardStats();

        // Get recent activities
        $recentActivities = AdminActivityLog::with('user')
            ->recent(7)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get recent inquiries
        $recentInquiries = ContactInquiry::with('assignedUser')
            ->recent(30)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get chart data for the last 30 days
        $chartData = $this->getChartData();

        return view('admin.dashboard', compact('stats', 'recentActivities', 'recentInquiries', 'chartData'));
    }

    /**
     * Get dashboard statistics.
     */
    private function getDashboardStats(): array
    {
        $today = Carbon::today();
        $thisMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_users' => User::count(),
            'new_users_today' => User::whereDate('created_at', $today)->count(),
            'new_users_this_month' => User::where('created_at', '>=', $thisMonth)->count(),

            'total_blog_posts' => BlogPost::count(),
            'published_posts' => BlogPost::published()->count(),
            'draft_posts' => BlogPost::where('status', 'draft')->count(),

            'total_services' => Service::count(),
            'active_services' => Service::active()->count(),

            'total_testimonials' => Testimonial::count(),
            'active_testimonials' => Testimonial::active()->count(),

            'total_inquiries' => ContactInquiry::count(),
            'new_inquiries' => ContactInquiry::new()->count(),
            'inquiries_today' => ContactInquiry::whereDate('created_at', $today)->count(),
            'inquiries_this_month' => ContactInquiry::where('created_at', '>=', $thisMonth)->count(),
            'inquiries_last_month' => ContactInquiry::whereBetween('created_at', [$lastMonth, $thisMonth])->count(),
        ];
    }

    /**
     * Get chart data for dashboard.
     */
    private function getChartData(): array
    {
        $days = collect();
        $inquiries = collect();
        $users = collect();

        // Get data for the last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $days->push($date->format('M d'));

            $inquiries->push(ContactInquiry::whereDate('created_at', $date)->count());
            $users->push(User::whereDate('created_at', $date)->count());
        }

        return [
            'labels' => $days->toArray(),
            'inquiries' => $inquiries->toArray(),
            'users' => $users->toArray(),
        ];
    }

    /**
     * Upload image file.
     */
    public function uploadImage(Request $request): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            $file = $request->file('image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('uploads/images', $filename, 'public');

            return response()->json([
                'success' => true,
                'path' => $path,
                'url' => Storage::url($path),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload image: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload general file.
     */
    public function uploadFile(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        try {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('uploads/files', $filename, 'public');

            return response()->json([
                'success' => true,
                'path' => $path,
                'url' => Storage::url($path),
                'filename' => $filename,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file: ' . $e->getMessage(),
            ], 500);
        }
    }
}
