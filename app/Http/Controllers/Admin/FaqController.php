<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_faqs')) {
            abort(403, 'You do not have permission to view FAQs.');
        }

        $faqs = Faq::when(request('search'), function ($query, $search) {
                $query->where('question', 'like', "%{$search}%")
                      ->orWhere('answer', 'like', "%{$search}%");
            })
            ->when(request('category'), function ($query, $category) {
                $query->where('category', $category);
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $categories = Faq::distinct()->pluck('category')->filter();

        return view('admin.faqs.index', compact('faqs', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_faqs')) {
            abort(403, 'You do not have permission to create FAQs.');
        }

        return view('admin.faqs.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_faqs')) {
            abort(403, 'You do not have permission to create FAQs.');
        }

        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'category' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $faq = Faq::create($validated);

        AdminActivityLog::log('created', $faq, "Created FAQ: {$faq->question}");

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Faq $faq)
    {
        if (!auth()->user()->hasPermission('view_faqs')) {
            abort(403, 'You do not have permission to view FAQs.');
        }

        return view('admin.faqs.show', compact('faq'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Faq $faq)
    {
        if (!auth()->user()->hasPermission('edit_faqs')) {
            abort(403, 'You do not have permission to edit FAQs.');
        }

        return view('admin.faqs.edit', compact('faq'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Faq $faq)
    {
        if (!auth()->user()->hasPermission('edit_faqs')) {
            abort(403, 'You do not have permission to edit FAQs.');
        }

        $validated = $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'category' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $oldValues = $faq->toArray();
        $faq->update($validated);

        AdminActivityLog::log('updated', $faq, "Updated FAQ: {$faq->question}", $oldValues, $faq->toArray());

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Faq $faq)
    {
        if (!auth()->user()->hasPermission('delete_faqs')) {
            abort(403, 'You do not have permission to delete FAQs.');
        }

        $question = $faq->question;
        $faq->delete();

        AdminActivityLog::log('deleted', null, "Deleted FAQ: {$question}");

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ deleted successfully.');
    }

    /**
     * Reorder FAQs.
     */
    public function reorder(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_faqs')) {
            abort(403, 'You do not have permission to reorder FAQs.');
        }

        $request->validate([
            'faqs' => 'required|array',
            'faqs.*.id' => 'required|exists:faqs,id',
            'faqs.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->faqs as $faqData) {
            Faq::where('id', $faqData['id'])
                ->update(['sort_order' => $faqData['sort_order']]);
        }

        AdminActivityLog::log('updated', null, 'Reordered FAQs');

        return response()->json(['success' => true]);
    }
}
