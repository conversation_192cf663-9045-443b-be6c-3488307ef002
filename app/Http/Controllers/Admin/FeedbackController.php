<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Feedback;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;

class FeedbackController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if (!auth()->user()->hasPermission('view_feedback')) {
            abort(403, 'You do not have permission to view feedback.');
        }

        $query = Feedback::with('respondedBy')->latest();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $feedback = $query->paginate(20);

        // Statistics
        $stats = [
            'total' => Feedback::count(),
            'new' => Feedback::where('status', 'new')->count(),
            'in_progress' => Feedback::where('status', 'in_progress')->count(),
            'resolved' => Feedback::where('status', 'resolved')->count(),
        ];

        return view('admin.feedback.index', compact('feedback', 'stats'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Feedback $feedback)
    {
        if (!auth()->user()->hasPermission('view_feedback')) {
            abort(403, 'You do not have permission to view feedback.');
        }

        $feedback->load('respondedBy');

        return view('admin.feedback.show', compact('feedback'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Feedback $feedback)
    {
        if (!auth()->user()->hasPermission('edit_feedback')) {
            abort(403, 'You do not have permission to edit feedback.');
        }

        $validated = $request->validate([
            'status' => 'required|in:new,in_progress,resolved,closed',
            'admin_notes' => 'nullable|string',
            'is_public' => 'boolean',
        ]);

        $oldValues = $feedback->toArray();

        // If status is being changed to resolved, mark as responded
        if ($validated['status'] === 'resolved' && $feedback->status !== 'resolved') {
            $validated['responded_at'] = now();
            $validated['responded_by'] = auth()->id();
        }

        $feedback->update($validated);

        AdminActivityLog::log('updated', $feedback, "Updated feedback from {$feedback->name}", $oldValues, $feedback->toArray());

        return redirect()->route('admin.feedback.show', $feedback)
            ->with('success', 'Feedback updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Feedback $feedback)
    {
        if (!auth()->user()->hasPermission('delete_feedback')) {
            abort(403, 'You do not have permission to delete feedback.');
        }

        $name = $feedback->name;
        $feedback->delete();

        AdminActivityLog::log('deleted', null, "Deleted feedback from {$name}");

        return redirect()->route('admin.feedback.index')
            ->with('success', 'Feedback deleted successfully.');
    }

    /**
     * Update feedback status.
     */
    public function updateStatus(Request $request, Feedback $feedback)
    {
        if (!auth()->user()->hasPermission('edit_feedback')) {
            abort(403, 'You do not have permission to edit feedback.');
        }

        $validated = $request->validate([
            'status' => 'required|in:new,in_progress,resolved,closed',
        ]);

        $oldStatus = $feedback->status;

        // If status is being changed to resolved, mark as responded
        if ($validated['status'] === 'resolved' && $feedback->status !== 'resolved') {
            $feedback->update([
                'status' => $validated['status'],
                'responded_at' => now(),
                'responded_by' => auth()->id(),
            ]);
        } else {
            $feedback->update($validated);
        }

        AdminActivityLog::log('updated', $feedback, "Changed feedback status from {$oldStatus} to {$validated['status']}");

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully.',
        ]);
    }

    /**
     * Mark feedback as public/private.
     */
    public function togglePublic(Feedback $feedback)
    {
        if (!auth()->user()->hasPermission('edit_feedback')) {
            abort(403, 'You do not have permission to edit feedback.');
        }

        $feedback->update(['is_public' => !$feedback->is_public]);

        AdminActivityLog::log('updated', $feedback, "Toggled feedback public status to " . ($feedback->is_public ? 'public' : 'private'));

        return response()->json([
            'success' => true,
            'is_public' => $feedback->is_public,
        ]);
    }
}
