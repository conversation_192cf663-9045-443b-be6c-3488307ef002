<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Permission;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_roles')) {
            abort(403, 'You do not have permission to view roles.');
        }

        $roles = Role::withCount(['users', 'permissions'])
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_roles')) {
            abort(403, 'You do not have permission to create roles.');
        }

        $permissions = Permission::active()->orderBy('group')->orderBy('name')->get();
        $permissionGroups = $permissions->groupBy('group');

        return view('admin.roles.create', compact('permissions', 'permissionGroups'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_roles')) {
            abort(403, 'You do not have permission to create roles.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        $role = Role::create($validated);

        if (!empty($validated['permissions'])) {
            $role->permissions()->sync($validated['permissions']);
        }

        AdminActivityLog::log('created', $role, "Created role: {$role->display_name}");

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role)
    {
        if (!auth()->user()->hasPermission('view_roles')) {
            abort(403, 'You do not have permission to view roles.');
        }

        $role->load(['permissions', 'users']);
        $permissionGroups = $role->permissions->groupBy('group');

        return view('admin.roles.show', compact('role', 'permissionGroups'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Role $role)
    {
        if (!auth()->user()->hasPermission('edit_roles')) {
            abort(403, 'You do not have permission to edit roles.');
        }

        // Prevent editing super admin role by non-super admin
        if ($role->name === 'super_admin' && auth()->user()->role->name !== 'super_admin') {
            abort(403, 'You cannot edit the super administrator role.');
        }

        $permissions = Permission::active()->orderBy('group')->orderBy('name')->get();
        $permissionGroups = $permissions->groupBy('group');
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('admin.roles.edit', compact('role', 'permissions', 'permissionGroups', 'rolePermissions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Role $role)
    {
        if (!auth()->user()->hasPermission('edit_roles')) {
            abort(403, 'You do not have permission to edit roles.');
        }

        // Prevent editing super admin role by non-super admin
        if ($role->name === 'super_admin' && auth()->user()->role->name !== 'super_admin') {
            abort(403, 'You cannot edit the super administrator role.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        $oldValues = $role->toArray();
        $oldPermissions = $role->permissions->pluck('id')->toArray();

        $role->update($validated);

        if (isset($validated['permissions'])) {
            $role->permissions()->sync($validated['permissions']);
        } else {
            $role->permissions()->sync([]);
        }

        AdminActivityLog::log('updated', $role, "Updated role: {$role->display_name}", $oldValues, $role->toArray());

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role)
    {
        if (!auth()->user()->hasPermission('delete_roles')) {
            abort(403, 'You do not have permission to delete roles.');
        }

        // Prevent deleting system roles
        if (in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer'])) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete system roles.');
        }

        // Check if role has users
        if ($role->users()->count() > 0) {
            return redirect()->route('admin.roles.index')
                ->with('error', 'Cannot delete role that has assigned users.');
        }

        $name = $role->display_name;
        $role->delete();

        AdminActivityLog::log('deleted', null, "Deleted role: {$name}");

        return redirect()->route('admin.roles.index')
            ->with('success', 'Role deleted successfully.');
    }

    /**
     * Update role permissions.
     */
    public function updatePermissions(Request $request, Role $role)
    {
        if (!auth()->user()->hasPermission('edit_roles')) {
            abort(403, 'You do not have permission to edit roles.');
        }

        // Prevent editing super admin role by non-super admin
        if ($role->name === 'super_admin' && auth()->user()->role->name !== 'super_admin') {
            abort(403, 'You cannot edit the super administrator role.');
        }

        $validated = $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $oldPermissions = $role->permissions->pluck('id')->toArray();

        if (isset($validated['permissions'])) {
            $role->permissions()->sync($validated['permissions']);
        } else {
            $role->permissions()->sync([]);
        }

        AdminActivityLog::log('updated', $role, "Updated permissions for role: {$role->display_name}");

        return response()->json([
            'success' => true,
            'message' => 'Permissions updated successfully.'
        ]);
    }
}
