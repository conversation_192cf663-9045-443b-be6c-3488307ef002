<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ServiceCategory;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ServiceCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_services')) {
            abort(403, 'You do not have permission to view service categories.');
        }

        $categories = ServiceCategory::withCount('services')
            ->ordered()
            ->paginate(20);

        return view('admin.service-categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_services')) {
            abort(403, 'You do not have permission to create service categories.');
        }

        return view('admin.service-categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_services')) {
            abort(403, 'You do not have permission to create service categories.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:service_categories,slug',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:100',
            'color_from' => 'required|string|max:50',
            'color_to' => 'required|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $category = ServiceCategory::create($validated);

        AdminActivityLog::log('created', $category, "Created service category: {$category->name}");

        return redirect()->route('admin.service-categories.index')
            ->with('success', 'Service category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ServiceCategory $serviceCategory)
    {
        if (!auth()->user()->hasPermission('view_services')) {
            abort(403, 'You do not have permission to view service categories.');
        }

        $serviceCategory->load('services');

        return view('admin.service-categories.show', compact('serviceCategory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ServiceCategory $serviceCategory)
    {
        if (!auth()->user()->hasPermission('edit_services')) {
            abort(403, 'You do not have permission to edit service categories.');
        }

        return view('admin.service-categories.edit', compact('serviceCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceCategory $serviceCategory)
    {
        if (!auth()->user()->hasPermission('edit_services')) {
            abort(403, 'You do not have permission to edit service categories.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:service_categories,slug,' . $serviceCategory->id,
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:100',
            'color_from' => 'required|string|max:50',
            'color_to' => 'required|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $oldValues = $serviceCategory->toArray();
        $serviceCategory->update($validated);

        AdminActivityLog::log('updated', $serviceCategory, "Updated service category: {$serviceCategory->name}", $oldValues, $serviceCategory->toArray());

        return redirect()->route('admin.service-categories.index')
            ->with('success', 'Service category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceCategory $serviceCategory)
    {
        if (!auth()->user()->hasPermission('delete_services')) {
            abort(403, 'You do not have permission to delete service categories.');
        }

        // Check if category has services
        if ($serviceCategory->services()->count() > 0) {
            return redirect()->route('admin.service-categories.index')
                ->with('error', 'Cannot delete service category that has services assigned to it.');
        }

        $name = $serviceCategory->name;
        $serviceCategory->delete();

        AdminActivityLog::log('deleted', null, "Deleted service category: {$name}");

        return redirect()->route('admin.service-categories.index')
            ->with('success', 'Service category deleted successfully.');
    }

    /**
     * Reorder service categories.
     */
    public function reorder(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_services')) {
            abort(403, 'You do not have permission to reorder service categories.');
        }

        $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:service_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->categories as $categoryData) {
            ServiceCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        AdminActivityLog::log('updated', null, 'Reordered service categories');

        return response()->json(['success' => true]);
    }
}
