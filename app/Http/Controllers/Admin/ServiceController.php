<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\ServiceCategory;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ServiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_services')) {
            abort(403, 'You do not have permission to view services.');
        }

        $services = Service::with('serviceCategory')
            ->orderBy('service_category_id')
            ->orderBy('sort_order')
            ->orderBy('title')
            ->paginate(20);

        return view('admin.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_services')) {
            abort(403, 'You do not have permission to create services.');
        }

        $categories = ServiceCategory::active()->ordered()->get();

        return view('admin.services.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_services')) {
            abort(403, 'You do not have permission to create services.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:services,slug',
            'description' => 'required|string',
            'features' => 'nullable|array',
            'service_category_id' => 'required|exists:service_categories,id',
            'category' => 'nullable|string|max:50', // Keep for backward compatibility
            'color_from' => 'required|string|max:50',
            'color_to' => 'required|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $service = Service::create($validated);

        AdminActivityLog::log('created', $service, "Created service: {$service->title}");

        return redirect()->route('admin.services.index')
            ->with('success', 'Service created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Service $service)
    {
        if (!auth()->user()->hasPermission('view_services')) {
            abort(403, 'You do not have permission to view services.');
        }

        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Service $service)
    {
        if (!auth()->user()->hasPermission('edit_services')) {
            abort(403, 'You do not have permission to edit services.');
        }

        $categories = ServiceCategory::active()->ordered()->get();

        return view('admin.services.edit', compact('service', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Service $service)
    {
        if (!auth()->user()->hasPermission('edit_services')) {
            abort(403, 'You do not have permission to edit services.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:services,slug,' . $service->id,
            'description' => 'required|string',
            'features' => 'nullable|array',
            'service_category_id' => 'required|exists:service_categories,id',
            'category' => 'nullable|string|max:50', // Keep for backward compatibility
            'color_from' => 'required|string|max:50',
            'color_to' => 'required|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        $oldValues = $service->toArray();
        $service->update($validated);

        AdminActivityLog::log('updated', $service, "Updated service: {$service->title}", $oldValues, $service->toArray());

        return redirect()->route('admin.services.index')
            ->with('success', 'Service updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Service $service)
    {
        if (!auth()->user()->hasPermission('delete_services')) {
            abort(403, 'You do not have permission to delete services.');
        }

        $title = $service->title;
        $service->delete();

        AdminActivityLog::log('deleted', null, "Deleted service: {$title}");

        return redirect()->route('admin.services.index')
            ->with('success', 'Service deleted successfully.');
    }

    /**
     * Reorder services.
     */
    public function reorder(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_services')) {
            abort(403, 'You do not have permission to reorder services.');
        }

        $request->validate([
            'services' => 'required|array',
            'services.*.id' => 'required|exists:services,id',
            'services.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->services as $serviceData) {
            Service::where('id', $serviceData['id'])
                ->update(['sort_order' => $serviceData['sort_order']]);
        }

        AdminActivityLog::log('updated', null, 'Reordered services');

        return response()->json(['success' => true]);
    }
}
