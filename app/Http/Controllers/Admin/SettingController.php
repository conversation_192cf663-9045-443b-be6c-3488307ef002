<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SettingController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_settings')) {
            abort(403, 'You do not have permission to view settings.');
        }

        $settings = SiteSetting::all()->pluck('value', 'key');

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update the settings.
     */
    public function update(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_settings')) {
            abort(403, 'You do not have permission to edit settings.');
        }

        $validated = $request->validate([
            // Site Information
            'site_name' => 'required|string|max:255',
            'site_tagline' => 'nullable|string|max:255',
            'site_description' => 'nullable|string|max:1000',
            'site_keywords' => 'nullable|string|max:500',
            'site_url' => 'required|url',

            // Company Information
            'company_name' => 'required|string|max:255',
            'company_address' => 'nullable|string|max:500',
            'company_city' => 'nullable|string|max:100',
            'company_state' => 'nullable|string|max:100',
            'company_country' => 'nullable|string|max:100',
            'company_postal_code' => 'nullable|string|max:20',

            // Contact Information
            'contact_email' => 'required|email',
            'contact_phone' => 'nullable|string|max:20',
            'contact_whatsapp' => 'nullable|string|max:20',
            'contact_address' => 'nullable|string|max:500',

            // Social Media
            'social_facebook' => 'nullable|url',
            'social_twitter' => 'nullable|url',
            'social_instagram' => 'nullable|url',
            'social_linkedin' => 'nullable|url',
            'social_youtube' => 'nullable|url',
            'social_tiktok' => 'nullable|url',

            // Business Hours
            'business_hours_monday' => 'nullable|string|max:100',
            'business_hours_tuesday' => 'nullable|string|max:100',
            'business_hours_wednesday' => 'nullable|string|max:100',
            'business_hours_thursday' => 'nullable|string|max:100',
            'business_hours_friday' => 'nullable|string|max:100',
            'business_hours_saturday' => 'nullable|string|max:100',
            'business_hours_sunday' => 'nullable|string|max:100',

            // SEO Settings
            'seo_title' => 'nullable|string|max:255',
            'seo_description' => 'nullable|string|max:500',
            'seo_keywords' => 'nullable|string|max:500',
            'google_analytics_id' => 'nullable|string|max:50',
            'google_tag_manager_id' => 'nullable|string|max:50',
            'facebook_pixel_id' => 'nullable|string|max:50',

            // Email Settings
            'email_from_name' => 'nullable|string|max:255',
            'email_from_address' => 'nullable|email',
            'email_reply_to' => 'nullable|email',

            // Maintenance
            'maintenance_mode' => 'boolean',
            'maintenance_message' => 'nullable|string|max:1000',

            // Features
            'enable_blog' => 'boolean',
            'enable_testimonials' => 'boolean',
            'enable_newsletter' => 'boolean',
            'enable_contact_form' => 'boolean',
        ]);

        $oldSettings = SiteSetting::all()->pluck('value', 'key')->toArray();

        // Define labels and groups for each setting
        $settingDefinitions = [
            // Site Information
            'site_name' => ['label' => 'Site Name', 'group' => 'site', 'type' => 'text'],
            'site_tagline' => ['label' => 'Site Tagline', 'group' => 'site', 'type' => 'text'],
            'site_description' => ['label' => 'Site Description', 'group' => 'site', 'type' => 'textarea'],
            'site_keywords' => ['label' => 'Site Keywords', 'group' => 'site', 'type' => 'text'],
            'site_url' => ['label' => 'Site URL', 'group' => 'site', 'type' => 'text'],

            // Company Information
            'company_name' => ['label' => 'Company Name', 'group' => 'company', 'type' => 'text'],
            'company_address' => ['label' => 'Company Address', 'group' => 'company', 'type' => 'text'],
            'company_city' => ['label' => 'Company City', 'group' => 'company', 'type' => 'text'],
            'company_state' => ['label' => 'Company State', 'group' => 'company', 'type' => 'text'],
            'company_country' => ['label' => 'Company Country', 'group' => 'company', 'type' => 'text'],
            'company_postal_code' => ['label' => 'Company Postal Code', 'group' => 'company', 'type' => 'text'],

            // Contact Information
            'contact_email' => ['label' => 'Contact Email', 'group' => 'contact', 'type' => 'text'],
            'contact_phone' => ['label' => 'Contact Phone', 'group' => 'contact', 'type' => 'text'],
            'contact_whatsapp' => ['label' => 'WhatsApp Number', 'group' => 'contact', 'type' => 'text'],
            'contact_address' => ['label' => 'Contact Address', 'group' => 'contact', 'type' => 'text'],

            // Social Media
            'social_facebook' => ['label' => 'Facebook URL', 'group' => 'social', 'type' => 'text'],
            'social_twitter' => ['label' => 'Twitter URL', 'group' => 'social', 'type' => 'text'],
            'social_instagram' => ['label' => 'Instagram URL', 'group' => 'social', 'type' => 'text'],
            'social_linkedin' => ['label' => 'LinkedIn URL', 'group' => 'social', 'type' => 'text'],
            'social_youtube' => ['label' => 'YouTube URL', 'group' => 'social', 'type' => 'text'],
            'social_tiktok' => ['label' => 'TikTok URL', 'group' => 'social', 'type' => 'text'],

            // Business Hours
            'business_hours_monday' => ['label' => 'Monday Hours', 'group' => 'hours', 'type' => 'text'],
            'business_hours_tuesday' => ['label' => 'Tuesday Hours', 'group' => 'hours', 'type' => 'text'],
            'business_hours_wednesday' => ['label' => 'Wednesday Hours', 'group' => 'hours', 'type' => 'text'],
            'business_hours_thursday' => ['label' => 'Thursday Hours', 'group' => 'hours', 'type' => 'text'],
            'business_hours_friday' => ['label' => 'Friday Hours', 'group' => 'hours', 'type' => 'text'],
            'business_hours_saturday' => ['label' => 'Saturday Hours', 'group' => 'hours', 'type' => 'text'],
            'business_hours_sunday' => ['label' => 'Sunday Hours', 'group' => 'hours', 'type' => 'text'],

            // SEO Settings
            'seo_title' => ['label' => 'SEO Title', 'group' => 'seo', 'type' => 'text'],
            'seo_description' => ['label' => 'SEO Description', 'group' => 'seo', 'type' => 'textarea'],
            'seo_keywords' => ['label' => 'SEO Keywords', 'group' => 'seo', 'type' => 'text'],
            'google_analytics_id' => ['label' => 'Google Analytics ID', 'group' => 'seo', 'type' => 'text'],
            'google_tag_manager_id' => ['label' => 'Google Tag Manager ID', 'group' => 'seo', 'type' => 'text'],
            'facebook_pixel_id' => ['label' => 'Facebook Pixel ID', 'group' => 'seo', 'type' => 'text'],

            // Email Settings
            'email_from_name' => ['label' => 'Email From Name', 'group' => 'email', 'type' => 'text'],
            'email_from_address' => ['label' => 'Email From Address', 'group' => 'email', 'type' => 'text'],
            'email_reply_to' => ['label' => 'Email Reply To', 'group' => 'email', 'type' => 'text'],

            // Maintenance
            'maintenance_mode' => ['label' => 'Maintenance Mode', 'group' => 'system', 'type' => 'boolean'],
            'maintenance_message' => ['label' => 'Maintenance Message', 'group' => 'system', 'type' => 'textarea'],

            // Features
            'enable_blog' => ['label' => 'Enable Blog', 'group' => 'features', 'type' => 'boolean'],
            'enable_testimonials' => ['label' => 'Enable Testimonials', 'group' => 'features', 'type' => 'boolean'],
            'enable_newsletter' => ['label' => 'Enable Newsletter', 'group' => 'features', 'type' => 'boolean'],
            'enable_contact_form' => ['label' => 'Enable Contact Form', 'group' => 'features', 'type' => 'boolean'],
        ];

        foreach ($validated as $key => $value) {
            $definition = $settingDefinitions[$key] ?? [
                'label' => ucfirst(str_replace('_', ' ', $key)),
                'group' => 'general',
                'type' => 'text'
            ];

            SiteSetting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'label' => $definition['label'],
                    'group' => $definition['group'],
                    'type' => $definition['type'],
                ]
            );
        }

        AdminActivityLog::log('updated', null, 'Updated site settings', $oldSettings, $validated);

        return redirect()->route('admin.settings.index')
            ->with('success', 'Settings updated successfully.');
    }

    /**
     * Upload logo.
     */
    public function uploadLogo(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_settings')) {
            abort(403, 'You do not have permission to edit settings.');
        }

        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        try {
            $file = $request->file('logo');
            $filename = 'logo.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('uploads/logos', $filename, 'public');

            SiteSetting::updateOrCreate(
                ['key' => 'site_logo'],
                [
                    'value' => $path,
                    'label' => 'Site Logo',
                    'group' => 'site',
                    'type' => 'file',
                ]
            );

            AdminActivityLog::log('updated', null, 'Updated site logo');

            return response()->json([
                'success' => true,
                'path' => $path,
                'url' => Storage::url($path),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload logo: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload favicon.
     */
    public function uploadFavicon(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_settings')) {
            abort(403, 'You do not have permission to edit settings.');
        }

        $request->validate([
            'favicon' => 'required|image|mimes:ico,png|max:1024',
        ]);

        try {
            $file = $request->file('favicon');
            $filename = 'favicon.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('uploads/logos', $filename, 'public');

            SiteSetting::updateOrCreate(
                ['key' => 'site_favicon'],
                [
                    'value' => $path,
                    'label' => 'Site Favicon',
                    'group' => 'site',
                    'type' => 'file',
                ]
            );

            AdminActivityLog::log('updated', null, 'Updated site favicon');

            return response()->json([
                'success' => true,
                'path' => $path,
                'url' => Storage::url($path),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload favicon: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clear cache.
     */
    public function clearCache()
    {
        if (!auth()->user()->hasPermission('edit_settings')) {
            abort(403, 'You do not have permission to clear cache.');
        }

        try {
            \Artisan::call('cache:clear');
            \Artisan::call('config:clear');
            \Artisan::call('route:clear');
            \Artisan::call('view:clear');

            AdminActivityLog::log('updated', null, 'Cleared application cache');

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage(),
            ], 500);
        }
    }
}
