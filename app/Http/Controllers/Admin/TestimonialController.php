<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Testimonial;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;

class TestimonialController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_testimonials')) {
            abort(403, 'You do not have permission to view testimonials.');
        }

        $testimonials = Testimonial::orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.testimonials.index', compact('testimonials'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_testimonials')) {
            abort(403, 'You do not have permission to create testimonials.');
        }

        return view('admin.testimonials.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_testimonials')) {
            abort(403, 'You do not have permission to create testimonials.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'content' => 'required|string',
            'rating' => 'required|integer|min:1|max:5',
            'avatar' => 'nullable|string',
            'service_type' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $testimonial = Testimonial::create($validated);

        AdminActivityLog::log('created', $testimonial, "Created testimonial: {$testimonial->name}");

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Testimonial $testimonial)
    {
        if (!auth()->user()->hasPermission('view_testimonials')) {
            abort(403, 'You do not have permission to view testimonials.');
        }

        return view('admin.testimonials.show', compact('testimonial'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Testimonial $testimonial)
    {
        if (!auth()->user()->hasPermission('edit_testimonials')) {
            abort(403, 'You do not have permission to edit testimonials.');
        }

        return view('admin.testimonials.edit', compact('testimonial'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Testimonial $testimonial)
    {
        if (!auth()->user()->hasPermission('edit_testimonials')) {
            abort(403, 'You do not have permission to edit testimonials.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'content' => 'required|string',
            'rating' => 'required|integer|min:1|max:5',
            'avatar' => 'nullable|string',
            'service_type' => 'nullable|string|max:100',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
        ]);

        $oldValues = $testimonial->toArray();
        $testimonial->update($validated);

        AdminActivityLog::log('updated', $testimonial, "Updated testimonial: {$testimonial->name}", $oldValues, $testimonial->toArray());

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Testimonial $testimonial)
    {
        if (!auth()->user()->hasPermission('delete_testimonials')) {
            abort(403, 'You do not have permission to delete testimonials.');
        }

        $name = $testimonial->name;
        $testimonial->delete();

        AdminActivityLog::log('deleted', null, "Deleted testimonial: {$name}");

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial deleted successfully.');
    }

    /**
     * Reorder testimonials.
     */
    public function reorder(Request $request)
    {
        if (!auth()->user()->hasPermission('edit_testimonials')) {
            abort(403, 'You do not have permission to reorder testimonials.');
        }

        $request->validate([
            'testimonials' => 'required|array',
            'testimonials.*.id' => 'required|exists:testimonials,id',
            'testimonials.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->testimonials as $testimonialData) {
            Testimonial::where('id', $testimonialData['id'])
                ->update(['sort_order' => $testimonialData['sort_order']]);
        }

        AdminActivityLog::log('updated', null, 'Reordered testimonials');

        return response()->json(['success' => true]);
    }
}
