<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Models\AdminActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->hasPermission('view_users')) {
            abort(403, 'You do not have permission to view users.');
        }

        $users = User::with('role')
            ->when(request('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
            })
            ->when(request('role'), function ($query, $role) {
                $query->where('role_id', $role);
            })
            ->when(request('status'), function ($query, $status) {
                $query->where('is_active', $status === 'active');
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $roles = Role::all();

        return view('admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->hasPermission('create_users')) {
            abort(403, 'You do not have permission to create users.');
        }

        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->hasPermission('create_users')) {
            abort(403, 'You do not have permission to create users.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role_id' => 'required|exists:roles,id',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        $validated['password'] = Hash::make($validated['password']);
        $validated['email_verified_at'] = now();

        $user = User::create($validated);

        AdminActivityLog::log('created', $user, "Created user: {$user->name}");

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        if (!auth()->user()->hasPermission('view_users')) {
            abort(403, 'You do not have permission to view users.');
        }

        $user->load('role');

        // Get recent activity logs for this user
        $recentActivities = AdminActivityLog::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.users.show', compact('user', 'recentActivities'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        if (!auth()->user()->hasPermission('edit_users')) {
            abort(403, 'You do not have permission to edit users.');
        }

        // Prevent editing super admin by non-super admin
        if ($user->role && $user->role->name === 'super_admin' && auth()->user()->role && auth()->user()->role->name !== 'super_admin') {
            abort(403, 'You cannot edit super administrator accounts.');
        }

        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        if (!auth()->user()->hasPermission('edit_users')) {
            abort(403, 'You do not have permission to edit users.');
        }

        // Prevent editing super admin by non-super admin
        if ($user->role->name === 'super_admin' && auth()->user()->role->name !== 'super_admin') {
            abort(403, 'You cannot edit super administrator accounts.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'role_id' => 'required|exists:roles,id',
            'phone' => 'nullable|string|max:20',
            'is_active' => 'boolean',
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $oldValues = $user->toArray();
        $user->update($validated);

        AdminActivityLog::log('updated', $user, "Updated user: {$user->name}", $oldValues, $user->toArray());

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        if (!auth()->user()->hasPermission('delete_users')) {
            abort(403, 'You do not have permission to delete users.');
        }

        // Prevent deleting super admin
        if ($user->role->name === 'super_admin') {
            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete super administrator account.');
        }

        // Prevent deleting yourself
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        $name = $user->name;
        $user->delete();

        AdminActivityLog::log('deleted', null, "Deleted user: {$name}");

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status.
     */
    public function toggleStatus(User $user)
    {
        if (!auth()->user()->hasPermission('edit_users')) {
            abort(403, 'You do not have permission to edit users.');
        }

        // Prevent deactivating super admin
        if ($user->role && $user->role->name === 'super_admin') {
            return response()->json(['error' => 'Cannot deactivate super administrator account.'], 403);
        }

        // Prevent deactivating yourself
        if ($user->id === auth()->id()) {
            return response()->json(['error' => 'You cannot deactivate your own account.'], 403);
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';
        AdminActivityLog::log('updated', $user, "User {$status}: {$user->name}");

        return response()->json([
            'success' => true,
            'status' => $user->is_active,
            'message' => "User {$status} successfully."
        ]);
    }
}
