<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ServiceCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $categories = ServiceCategory::active()
            ->withCount('services')
            ->ordered()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:100',
            'color_from' => 'required|string|max:50',
            'color_to' => 'required|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $category = ServiceCategory::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Service category created successfully.',
            'data' => $category,
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ServiceCategory $serviceCategory): JsonResponse
    {
        $serviceCategory->load('services');

        return response()->json([
            'success' => true,
            'data' => $serviceCategory,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ServiceCategory $serviceCategory): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:100',
            'color_from' => 'required|string|max:50',
            'color_to' => 'required|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $serviceCategory->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Service category updated successfully.',
            'data' => $serviceCategory,
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ServiceCategory $serviceCategory): JsonResponse
    {
        // Check if category has services
        if ($serviceCategory->services()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete service category that has services assigned to it.',
            ], 422);
        }

        $serviceCategory->delete();

        return response()->json([
            'success' => true,
            'message' => 'Service category deleted successfully.',
        ]);
    }
}
