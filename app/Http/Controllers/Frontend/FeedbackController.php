<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Feedback;
use Illuminate\Http\Request;

class FeedbackController extends Controller
{
    /**
     * Show the feedback form.
     */
    public function create()
    {
        return view('pages.feedback');
    }

    /**
     * Store feedback submission.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'type' => 'required|in:general,complaint,suggestion,compliment,inquiry',
            'rating' => 'nullable|integer|min:1|max:5',
            'service_type' => 'nullable|string|max:100',
        ]);

        // Add IP and user agent for tracking
        $validated['ip_address'] = $request->ip();
        $validated['user_agent'] = $request->userAgent();

        Feedback::create($validated);

        return redirect()->route('feedback.create')
            ->with('success', 'Thank you for your feedback! We will review it and get back to you soon.');
    }

    /**
     * Show public feedback/testimonials.
     */
    public function public()
    {
        $feedback = Feedback::public()
            ->withRating()
            ->where('status', 'resolved')
            ->latest()
            ->paginate(12);

        return view('pages.feedback-public', compact('feedback'));
    }
}
