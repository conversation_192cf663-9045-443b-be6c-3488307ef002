<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\ServiceCategory;
use App\Models\BlogPost;
use App\Models\Faq;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Display the home page.
     */
    public function home()
    {
        $featuredServices = Service::with('serviceCategory')
            ->active()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();

        $recentBlogPosts = BlogPost::with('author')
            ->published()
            ->recent(3)
            ->get();

        $testimonials = Testimonial::active()
            ->featured()
            ->limit(3)
            ->get();

        return view('pages.home', compact('featuredServices', 'recentBlogPosts', 'testimonials'));
    }

    /**
     * Display the services page.
     */
    public function services()
    {
        $serviceCategories = ServiceCategory::with(['services' => function ($query) {
            $query->active()->ordered();
        }])
        ->active()
        ->ordered()
        ->get();

        return view('pages.services', compact('serviceCategories'));
    }

    /**
     * Display the blog index page.
     */
    public function blogIndex()
    {
        $featuredPost = BlogPost::with('author')
            ->published()
            ->featured()
            ->latest('published_at')
            ->first();

        $blogPosts = BlogPost::with('author')
            ->published()
            ->when($featuredPost, function ($query) use ($featuredPost) {
                return $query->where('id', '!=', $featuredPost->id);
            })
            ->latest('published_at')
            ->paginate(9);

        $categories = BlogPost::published()
            ->select('category')
            ->distinct()
            ->pluck('category');

        return view('pages.blog.index', compact('featuredPost', 'blogPosts', 'categories'));
    }

    /**
     * Display a single blog post.
     */
    public function blogSingle($slug)
    {
        $blogPost = BlogPost::with('author')
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        // Increment view count
        $blogPost->incrementViews();

        // Get related posts
        $relatedPosts = BlogPost::with('author')
            ->published()
            ->where('id', '!=', $blogPost->id)
            ->where('category', $blogPost->category)
            ->latest('published_at')
            ->limit(3)
            ->get();

        return view('pages.blog.single', compact('blogPost', 'relatedPosts'));
    }

    /**
     * Display the FAQ page.
     */
    public function faq()
    {
        $faqs = Faq::active()
            ->ordered()
            ->get()
            ->groupBy('category');

        return view('pages.faq', compact('faqs'));
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        $testimonials = Testimonial::active()
            ->featured()
            ->limit(6)
            ->get();

        return view('pages.about', compact('testimonials'));
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('pages.contact');
    }
}
