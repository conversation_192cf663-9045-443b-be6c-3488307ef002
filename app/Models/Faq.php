<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Faq extends Model
{
    protected $table = 'faqs';

    protected $fillable = [
        'question',
        'answer',
        'category',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Scope for active FAQs.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for FAQs by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for ordered FAQs.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('question');
    }

    /**
     * Get available categories.
     */
    public static function getCategories(): array
    {
        return [
            'general' => 'General',
            'services' => 'Services',
            'travel' => 'Travel',
            'study_abroad' => 'Study Abroad',
            'visa' => 'Visa',
            'payment' => 'Payment',
        ];
    }
}
