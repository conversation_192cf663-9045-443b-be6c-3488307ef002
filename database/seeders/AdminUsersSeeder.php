<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get role IDs
        $superAdminRole = DB::table('roles')->where('name', 'super_admin')->first();
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        $editorRole = DB::table('roles')->where('name', 'editor')->first();
        $viewerRole = DB::table('roles')->where('name', 'viewer')->first();

        // Create admin users
        $users = [
            [
                'name' => 'Super Administrator',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('SuperAdmin@2024'),
                'role_id' => $superAdminRole->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Global Ventures Admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('Admin@2024'),
                'role_id' => $adminRole->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Content Editor',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('Editor@2024'),
                'role_id' => $editorRole->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Content Viewer',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => Hash::make('Viewer@2024'),
                'role_id' => $viewerRole->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($users as $user) {
            DB::table('users')->insert($user);
        }
    }
}
