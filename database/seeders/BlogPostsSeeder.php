<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogPost;
use App\Models\User;
use Carbon\Carbon;

class BlogPostsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin users to assign as authors
        $authors = User::whereHas('role', function ($query) {
            $query->whereIn('name', ['super_admin', 'admin', 'editor']);
        })->get();

        if ($authors->isEmpty()) {
            // Fallback to any user if no admin users exist
            $authors = User::take(3)->get();
        }

        $blogPosts = [
            [
                'title' => 'Your Complete Guide to Working in Dubai: Opportunities and Requirements',
                'slug' => 'complete-guide-working-dubai-opportunities-requirements',
                'excerpt' => 'Discover the thriving job market in Dubai, visa requirements, and how Global Ventures can help you secure your dream job in the UAE.',
                'content' => $this->getDubaiWorkContent(),
                'category' => 'Work Abroad',
                'tags' => ['Dubai', 'UAE', 'Work Visa', 'Job Opportunities', 'Middle East'],
                'status' => 'published',
                'is_featured' => true,
                'published_at' => Carbon::now()->subDays(5),
            ],
            [
                'title' => 'Top Universities in India for International Students: A Comprehensive Guide',
                'slug' => 'top-universities-india-international-students-guide',
                'excerpt' => 'Explore the best educational institutions in India, admission requirements, and scholarship opportunities for international students.',
                'content' => $this->getIndiaStudyContent(),
                'category' => 'Study Abroad',
                'tags' => ['India', 'Universities', 'Education', 'Student Visa', 'Scholarships'],
                'status' => 'published',
                'is_featured' => true,
                'published_at' => Carbon::now()->subDays(10),
            ],
            [
                'title' => 'Visa Application Process: Common Mistakes to Avoid',
                'slug' => 'visa-application-process-common-mistakes-avoid',
                'excerpt' => 'Learn about the most common visa application mistakes and how to avoid them to increase your chances of approval.',
                'content' => $this->getVisaProcessContent(),
                'category' => 'Visa Services',
                'tags' => ['Visa Application', 'Documentation', 'Travel Tips', 'Immigration'],
                'status' => 'published',
                'is_featured' => false,
                'published_at' => Carbon::now()->subDays(15),
            ],
            [
                'title' => 'Travel Insurance: Why It\'s Essential for International Travel',
                'slug' => 'travel-insurance-essential-international-travel',
                'excerpt' => 'Understanding the importance of travel insurance and what coverage you need for different types of international trips.',
                'content' => $this->getTravelInsuranceContent(),
                'category' => 'Travel Services',
                'tags' => ['Travel Insurance', 'International Travel', 'Safety', 'Coverage'],
                'status' => 'published',
                'is_featured' => false,
                'published_at' => Carbon::now()->subDays(20),
            ],
            [
                'title' => 'Cultural Adaptation: Tips for Living and Working Abroad',
                'slug' => 'cultural-adaptation-tips-living-working-abroad',
                'excerpt' => 'Essential advice for adapting to new cultures when moving abroad for work or study opportunities.',
                'content' => $this->getCulturalAdaptationContent(),
                'category' => 'Work Abroad',
                'tags' => ['Cultural Adaptation', 'Expat Life', 'International Living', 'Career'],
                'status' => 'published',
                'is_featured' => false,
                'published_at' => Carbon::now()->subDays(25),
            ],
            [
                'title' => 'Student Life in India: What to Expect as an International Student',
                'slug' => 'student-life-india-international-student-experience',
                'excerpt' => 'A comprehensive look at student life in India, from accommodation to cultural experiences and academic expectations.',
                'content' => $this->getStudentLifeContent(),
                'category' => 'Study Abroad',
                'tags' => ['Student Life', 'India', 'International Students', 'Culture', 'Education'],
                'status' => 'published',
                'is_featured' => false,
                'published_at' => Carbon::now()->subDays(30),
            ],
            [
                'title' => 'Budget Travel Tips: How to Explore More While Spending Less',
                'slug' => 'budget-travel-tips-explore-more-spend-less',
                'excerpt' => 'Practical tips and strategies for budget-conscious travelers who want to maximize their travel experiences.',
                'content' => $this->getBudgetTravelContent(),
                'category' => 'Travel Services',
                'tags' => ['Budget Travel', 'Travel Tips', 'Money Saving', 'Backpacking'],
                'status' => 'published',
                'is_featured' => false,
                'published_at' => Carbon::now()->subDays(35),
            ],
            [
                'title' => 'Document Checklist: Essential Papers for International Travel',
                'slug' => 'document-checklist-essential-papers-international-travel',
                'excerpt' => 'A comprehensive checklist of all the documents you need for international travel, work, and study abroad.',
                'content' => $this->getDocumentChecklistContent(),
                'category' => 'Visa Services',
                'tags' => ['Documentation', 'Travel Documents', 'Visa Requirements', 'Checklist'],
                'status' => 'published',
                'is_featured' => false,
                'published_at' => Carbon::now()->subDays(40),
            ],
            [
                'title' => 'Success Stories: How Global Ventures Changed Lives',
                'slug' => 'success-stories-global-ventures-changed-lives',
                'excerpt' => 'Read inspiring success stories from our clients who achieved their dreams of working and studying abroad.',
                'content' => $this->getSuccessStoriesContent(),
                'category' => 'Success Stories',
                'tags' => ['Success Stories', 'Client Testimonials', 'Inspiration', 'Achievement'],
                'status' => 'published',
                'is_featured' => true,
                'published_at' => Carbon::now()->subDays(45),
            ],
            [
                'title' => 'Future Trends in International Education and Work Migration',
                'slug' => 'future-trends-international-education-work-migration',
                'excerpt' => 'Exploring the emerging trends in global education and work migration, and what they mean for aspiring international students and workers.',
                'content' => $this->getFutureTrendsContent(),
                'category' => 'Industry Insights',
                'tags' => ['Future Trends', 'Education', 'Migration', 'Industry Analysis'],
                'status' => 'draft',
                'is_featured' => false,
                'published_at' => null,
            ],
        ];

        foreach ($blogPosts as $index => $postData) {
            // Assign random author from available authors
            $postData['author_id'] = $authors->random()->id;

            BlogPost::create($postData);
        }
    }

    private function getDubaiWorkContent(): string
    {
        return '<h2>Why Choose Dubai for Your Career?</h2>
        <p>Dubai has emerged as one of the world\'s leading business hubs, offering exceptional opportunities for international professionals. With its tax-free income, multicultural environment, and world-class infrastructure, Dubai attracts talent from around the globe.</p>

        <h3>Key Industries in Dubai</h3>
        <ul>
        <li><strong>Finance and Banking:</strong> Dubai International Financial Centre (DIFC) hosts major global banks</li>
        <li><strong>Tourism and Hospitality:</strong> World-class hotels and attractions create numerous opportunities</li>
        <li><strong>Technology:</strong> Growing tech sector with startups and established companies</li>
        <li><strong>Healthcare:</strong> Expanding medical sector with international standards</li>
        <li><strong>Construction and Real Estate:</strong> Ongoing development projects</li>
        </ul>

        <h3>Visa Requirements</h3>
        <p>To work in Dubai, you\'ll need:</p>
        <ul>
        <li>Employment visa sponsored by your employer</li>
        <li>Medical fitness certificate</li>
        <li>Educational certificate attestation</li>
        <li>Emirates ID registration</li>
        </ul>

        <p>Global Ventures specializes in helping professionals navigate the Dubai job market and visa process. Contact us today to start your journey!</p>';
    }

    private function getIndiaStudyContent(): string
    {
        return '<h2>India: A Rising Destination for International Education</h2>
        <p>India has become increasingly popular among international students due to its high-quality education, affordable costs, and rich cultural heritage. With over 1000 universities and 40,000 colleges, India offers diverse academic opportunities.</p>

        <h3>Top Universities for International Students</h3>
        <ul>
        <li><strong>Indian Institute of Technology (IIT):</strong> Premier engineering and technology institutes</li>
        <li><strong>Indian Institute of Management (IIM):</strong> Leading business schools</li>
        <li><strong>University of Delhi:</strong> Comprehensive university with diverse programs</li>
        <li><strong>Jawaharlal Nehru University:</strong> Excellence in social sciences and languages</li>
        <li><strong>Manipal Academy of Higher Education:</strong> Private university with global recognition</li>
        </ul>

        <h3>Admission Requirements</h3>
        <p>General requirements include:</p>
        <ul>
        <li>Academic transcripts and certificates</li>
        <li>English proficiency test scores (IELTS/TOEFL)</li>
        <li>Statement of purpose</li>
        <li>Letters of recommendation</li>
        <li>Student visa application</li>
        </ul>

        <p>Let Global Ventures guide you through the admission process and help you secure your place at a top Indian university.</p>';
    }

    private function getVisaProcessContent(): string
    {
        return '<h2>Common Visa Application Mistakes</h2>
        <p>Visa applications can be complex, and small mistakes can lead to delays or rejections. Here are the most common errors applicants make and how to avoid them.</p>

        <h3>Documentation Errors</h3>
        <ul>
        <li><strong>Incomplete forms:</strong> Always double-check every field</li>
        <li><strong>Incorrect photographs:</strong> Follow size and format requirements exactly</li>
        <li><strong>Missing supporting documents:</strong> Create a comprehensive checklist</li>
        <li><strong>Expired documents:</strong> Ensure all documents are current</li>
        </ul>

        <h3>Financial Documentation Issues</h3>
        <ul>
        <li>Insufficient bank statements</li>
        <li>Unclear source of funds</li>
        <li>Missing sponsor documentation</li>
        <li>Inconsistent financial information</li>
        </ul>

        <h3>Interview Preparation</h3>
        <p>If an interview is required:</p>
        <ul>
        <li>Be honest and consistent</li>
        <li>Prepare for common questions</li>
        <li>Bring all original documents</li>
        <li>Dress professionally</li>
        </ul>

        <p>Global Ventures provides comprehensive visa assistance to help you avoid these common pitfalls and increase your approval chances.</p>';
    }

    private function getTravelInsuranceContent(): string
    {
        return '<h2>The Importance of Travel Insurance</h2>
        <p>Travel insurance is often overlooked but is crucial for international travel. It provides financial protection against unexpected events that could otherwise ruin your trip and drain your savings.</p>

        <h3>What Does Travel Insurance Cover?</h3>
        <ul>
        <li><strong>Medical Emergencies:</strong> Hospital bills, doctor visits, emergency evacuation</li>
        <li><strong>Trip Cancellation:</strong> Non-refundable expenses if you need to cancel</li>
        <li><strong>Lost Luggage:</strong> Compensation for lost, stolen, or damaged belongings</li>
        <li><strong>Flight Delays:</strong> Additional accommodation and meal expenses</li>
        <li><strong>Personal Liability:</strong> Coverage for accidental damage to property</li>
        </ul>

        <p>Choose the right coverage based on your destination, activities, and trip duration. Global Ventures can help you select the perfect travel insurance policy.</p>';
    }

    private function getCulturalAdaptationContent(): string
    {
        return '<h2>Adapting to Life Abroad</h2>
        <p>Moving to a new country for work or study can be exciting but challenging. Cultural adaptation is key to your success and happiness abroad.</p>

        <h3>Before You Go</h3>
        <ul>
        <li>Research local customs and etiquette</li>
        <li>Learn basic phrases in the local language</li>
        <li>Connect with expat communities online</li>
        <li>Understand workplace culture</li>
        </ul>

        <h3>After Arrival</h3>
        <ul>
        <li>Be patient with yourself during adjustment</li>
        <li>Join local clubs or activities</li>
        <li>Maintain connections with home</li>
        <li>Stay open-minded and curious</li>
        </ul>

        <p>Remember, cultural adaptation takes time. Global Ventures provides ongoing support to help you thrive in your new environment.</p>';
    }

    private function getStudentLifeContent(): string
    {
        return '<h2>Student Life in India</h2>
        <p>India offers a unique and enriching experience for international students, combining academic excellence with cultural diversity.</p>

        <h3>Accommodation Options</h3>
        <ul>
        <li><strong>University Hostels:</strong> Affordable on-campus housing</li>
        <li><strong>Private Hostels:</strong> More amenities and flexibility</li>
        <li><strong>Shared Apartments:</strong> Independent living with roommates</li>
        <li><strong>Homestays:</strong> Live with local families</li>
        </ul>

        <h3>Cultural Experiences</h3>
        <p>India\'s rich heritage offers countless opportunities for cultural exploration, from festivals and cuisine to historical sites and diverse traditions.</p>

        <p>Global Ventures helps international students navigate their journey in India, from admission to graduation.</p>';
    }

    private function getBudgetTravelContent(): string
    {
        return '<h2>Smart Budget Travel Strategies</h2>
        <p>Traveling on a budget doesn\'t mean compromising on experiences. With careful planning and smart choices, you can explore the world affordably.</p>

        <h3>Money-Saving Tips</h3>
        <ul>
        <li>Book flights in advance or look for last-minute deals</li>
        <li>Use budget airlines for short distances</li>
        <li>Stay in hostels or budget accommodations</li>
        <li>Cook your own meals when possible</li>
        <li>Use public transportation</li>
        <li>Take advantage of free activities and attractions</li>
        </ul>

        <p>Global Ventures can help you find the best deals and plan budget-friendly trips without sacrificing quality.</p>';
    }

    private function getDocumentChecklistContent(): string
    {
        return '<h2>Essential Travel Documents</h2>
        <p>Having the right documents is crucial for international travel. Missing or incorrect paperwork can lead to denied entry or other complications.</p>

        <h3>Basic Requirements</h3>
        <ul>
        <li>Valid passport (6+ months validity)</li>
        <li>Appropriate visa or entry permit</li>
        <li>Return or onward ticket</li>
        <li>Proof of accommodation</li>
        <li>Travel insurance documentation</li>
        <li>Financial proof (bank statements)</li>
        </ul>

        <h3>Additional Documents</h3>
        <ul>
        <li>Educational certificates (for students)</li>
        <li>Employment letter (for workers)</li>
        <li>Medical certificates</li>
        <li>Vaccination records</li>
        </ul>

        <p>Let Global Ventures help you prepare all necessary documentation for your international journey.</p>';
    }

    private function getSuccessStoriesContent(): string
    {
        return '<h2>Real Success Stories</h2>
        <p>At Global Ventures, we\'re proud of our clients\' achievements. Here are some inspiring stories of dreams turned into reality.</p>

        <h3>Sarah\'s Journey to Dubai</h3>
        <p>"I never thought I could work in Dubai, but Global Ventures made it possible. They helped me find a job in finance and guided me through the visa process. Now I\'m living my dream in one of the world\'s most dynamic cities."</p>

        <h3>Raj\'s Education in India</h3>
        <p>"Studying at IIT was my dream, and Global Ventures helped me navigate the complex admission process. Their guidance was invaluable, and now I\'m pursuing my Master\'s in Computer Science."</p>

        <p>These are just a few examples of how Global Ventures has helped people achieve their international goals. Your success story could be next!</p>';
    }

    private function getFutureTrendsContent(): string
    {
        return '<h2>The Future of International Education and Migration</h2>
        <p>The landscape of international education and work migration is constantly evolving. Understanding these trends can help you make informed decisions about your future.</p>

        <h3>Emerging Trends</h3>
        <ul>
        <li>Digital nomad visas becoming more common</li>
        <li>Increased focus on STEM education</li>
        <li>Remote work changing migration patterns</li>
        <li>Sustainability in education and travel</li>
        <li>AI and technology transforming industries</li>
        </ul>

        <p>Stay ahead of these trends with Global Ventures\' expert guidance and industry insights.</p>';
    }
}
