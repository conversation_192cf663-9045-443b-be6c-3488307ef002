<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FeedbackPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Add feedback permissions
        $feedbackPermissions = [
            ['name' => 'view_feedback', 'display_name' => 'View Feedback', 'description' => 'View customer feedback', 'group' => 'feedback'],
            ['name' => 'edit_feedback', 'display_name' => 'Edit Feedback', 'description' => 'Edit feedback status and notes', 'group' => 'feedback'],
            ['name' => 'delete_feedback', 'display_name' => 'Delete Feedback', 'description' => 'Delete customer feedback', 'group' => 'feedback'],
        ];

        foreach ($feedbackPermissions as $permission) {
            DB::table('permissions')->insertOrIgnore(array_merge($permission, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Get all permissions
        $allPermissions = DB::table('permissions')->pluck('id', 'name');

        // Add feedback permissions to existing roles
        $roles = [
            'super_admin' => ['view_feedback', 'edit_feedback', 'delete_feedback'],
            'admin' => ['view_feedback', 'edit_feedback', 'delete_feedback'],
            'editor' => ['view_feedback', 'edit_feedback'],
            'viewer' => ['view_feedback'],
        ];

        foreach ($roles as $roleName => $permissions) {
            $role = DB::table('roles')->where('name', $roleName)->first();
            if ($role) {
                foreach ($permissions as $permissionName) {
                    if (isset($allPermissions[$permissionName])) {
                        DB::table('role_permission')->insertOrIgnore([
                            'role_id' => $role->id,
                            'permission_id' => $allPermissions[$permissionName],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }
                }
            }
        }
    }
}
