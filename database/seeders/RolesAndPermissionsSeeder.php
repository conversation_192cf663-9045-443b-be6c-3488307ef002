<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Dashboard
            ['name' => 'view_dashboard', 'display_name' => 'View Dashboard', 'description' => 'Access admin dashboard', 'group' => 'dashboard'],

            // Users Management
            ['name' => 'view_users', 'display_name' => 'View Users', 'description' => 'View user list', 'group' => 'users'],
            ['name' => 'create_users', 'display_name' => 'Create Users', 'description' => 'Create new users', 'group' => 'users'],
            ['name' => 'edit_users', 'display_name' => 'Edit Users', 'description' => 'Edit user details', 'group' => 'users'],
            ['name' => 'delete_users', 'display_name' => 'Delete Users', 'description' => 'Delete users', 'group' => 'users'],

            // Roles & Permissions
            ['name' => 'view_roles', 'display_name' => 'View Roles', 'description' => 'View roles list', 'group' => 'roles'],
            ['name' => 'create_roles', 'display_name' => 'Create Roles', 'description' => 'Create new roles', 'group' => 'roles'],
            ['name' => 'edit_roles', 'display_name' => 'Edit Roles', 'description' => 'Edit role details', 'group' => 'roles'],
            ['name' => 'delete_roles', 'display_name' => 'Delete Roles', 'description' => 'Delete roles', 'group' => 'roles'],

            // Services Management
            ['name' => 'view_services', 'display_name' => 'View Services', 'description' => 'View services list', 'group' => 'services'],
            ['name' => 'create_services', 'display_name' => 'Create Services', 'description' => 'Create new services', 'group' => 'services'],
            ['name' => 'edit_services', 'display_name' => 'Edit Services', 'description' => 'Edit service details', 'group' => 'services'],
            ['name' => 'delete_services', 'display_name' => 'Delete Services', 'description' => 'Delete services', 'group' => 'services'],

            // Blog Management
            ['name' => 'view_blog', 'display_name' => 'View Blog Posts', 'description' => 'View blog posts list', 'group' => 'blog'],
            ['name' => 'create_blog', 'display_name' => 'Create Blog Posts', 'description' => 'Create new blog posts', 'group' => 'blog'],
            ['name' => 'edit_blog', 'display_name' => 'Edit Blog Posts', 'description' => 'Edit blog posts', 'group' => 'blog'],
            ['name' => 'delete_blog', 'display_name' => 'Delete Blog Posts', 'description' => 'Delete blog posts', 'group' => 'blog'],
            ['name' => 'publish_blog', 'display_name' => 'Publish Blog Posts', 'description' => 'Publish/unpublish blog posts', 'group' => 'blog'],

            // Testimonials Management
            ['name' => 'view_testimonials', 'display_name' => 'View Testimonials', 'description' => 'View testimonials list', 'group' => 'testimonials'],
            ['name' => 'create_testimonials', 'display_name' => 'Create Testimonials', 'description' => 'Create new testimonials', 'group' => 'testimonials'],
            ['name' => 'edit_testimonials', 'display_name' => 'Edit Testimonials', 'description' => 'Edit testimonials', 'group' => 'testimonials'],
            ['name' => 'delete_testimonials', 'display_name' => 'Delete Testimonials', 'description' => 'Delete testimonials', 'group' => 'testimonials'],

            // FAQ Management
            ['name' => 'view_faqs', 'display_name' => 'View FAQs', 'description' => 'View FAQs list', 'group' => 'faqs'],
            ['name' => 'create_faqs', 'display_name' => 'Create FAQs', 'description' => 'Create new FAQs', 'group' => 'faqs'],
            ['name' => 'edit_faqs', 'display_name' => 'Edit FAQs', 'description' => 'Edit FAQs', 'group' => 'faqs'],
            ['name' => 'delete_faqs', 'display_name' => 'Delete FAQs', 'description' => 'Delete FAQs', 'group' => 'faqs'],

            // Contact Inquiries
            ['name' => 'view_inquiries', 'display_name' => 'View Inquiries', 'description' => 'View contact inquiries', 'group' => 'inquiries'],
            ['name' => 'manage_inquiries', 'display_name' => 'Manage Inquiries', 'description' => 'Manage contact inquiries', 'group' => 'inquiries'],
            ['name' => 'delete_inquiries', 'display_name' => 'Delete Inquiries', 'description' => 'Delete contact inquiries', 'group' => 'inquiries'],

            // Feedback Management
            ['name' => 'view_feedback', 'display_name' => 'View Feedback', 'description' => 'View customer feedback', 'group' => 'feedback'],
            ['name' => 'edit_feedback', 'display_name' => 'Edit Feedback', 'description' => 'Edit feedback status and notes', 'group' => 'feedback'],
            ['name' => 'delete_feedback', 'display_name' => 'Delete Feedback', 'description' => 'Delete customer feedback', 'group' => 'feedback'],

            // Settings
            ['name' => 'view_settings', 'display_name' => 'View Settings', 'description' => 'View site settings', 'group' => 'settings'],
            ['name' => 'edit_settings', 'display_name' => 'Edit Settings', 'description' => 'Edit site settings', 'group' => 'settings'],

            // Activity Logs
            ['name' => 'view_logs', 'display_name' => 'View Activity Logs', 'description' => 'View admin activity logs', 'group' => 'logs'],
        ];

        foreach ($permissions as $permission) {
            DB::table('permissions')->insert(array_merge($permission, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Create roles
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'Full access to all system features',
            ],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Access to most admin features',
            ],
            [
                'name' => 'editor',
                'display_name' => 'Editor',
                'description' => 'Can manage content but not users or settings',
            ],
            [
                'name' => 'viewer',
                'display_name' => 'Viewer',
                'description' => 'Read-only access to admin panel',
            ],
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insert(array_merge($role, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    private function assignPermissionsToRoles()
    {
        // Get all permissions
        $allPermissions = DB::table('permissions')->pluck('id', 'name');

        // Super Admin gets all permissions
        $superAdminRole = DB::table('roles')->where('name', 'super_admin')->first();
        foreach ($allPermissions as $permissionId) {
            DB::table('role_permission')->insert([
                'role_id' => $superAdminRole->id,
                'permission_id' => $permissionId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        // Admin permissions (all except user management)
        $adminRole = DB::table('roles')->where('name', 'admin')->first();
        $adminPermissions = [
            'view_dashboard', 'view_services', 'create_services', 'edit_services', 'delete_services',
            'view_blog', 'create_blog', 'edit_blog', 'delete_blog', 'publish_blog',
            'view_testimonials', 'create_testimonials', 'edit_testimonials', 'delete_testimonials',
            'view_faqs', 'create_faqs', 'edit_faqs', 'delete_faqs',
            'view_inquiries', 'manage_inquiries', 'delete_inquiries',
            'view_feedback', 'edit_feedback', 'delete_feedback',
            'view_settings', 'edit_settings', 'view_logs'
        ];

        foreach ($adminPermissions as $permission) {
            if (isset($allPermissions[$permission])) {
                DB::table('role_permission')->insert([
                    'role_id' => $adminRole->id,
                    'permission_id' => $allPermissions[$permission],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // Editor permissions (content management only)
        $editorRole = DB::table('roles')->where('name', 'editor')->first();
        $editorPermissions = [
            'view_dashboard', 'view_services', 'edit_services',
            'view_blog', 'create_blog', 'edit_blog', 'publish_blog',
            'view_testimonials', 'create_testimonials', 'edit_testimonials',
            'view_faqs', 'create_faqs', 'edit_faqs',
            'view_inquiries', 'manage_inquiries',
            'view_feedback', 'edit_feedback'
        ];

        foreach ($editorPermissions as $permission) {
            if (isset($allPermissions[$permission])) {
                DB::table('role_permission')->insert([
                    'role_id' => $editorRole->id,
                    'permission_id' => $allPermissions[$permission],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // Viewer permissions (read-only)
        $viewerRole = DB::table('roles')->where('name', 'viewer')->first();
        $viewerPermissions = [
            'view_dashboard', 'view_services', 'view_blog', 'view_testimonials',
            'view_faqs', 'view_inquiries', 'view_feedback', 'view_settings', 'view_logs'
        ];

        foreach ($viewerPermissions as $permission) {
            if (isset($allPermissions[$permission])) {
                DB::table('role_permission')->insert([
                    'role_id' => $viewerRole->id,
                    'permission_id' => $allPermissions[$permission],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }
}
