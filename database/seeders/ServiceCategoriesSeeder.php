<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ServiceCategory;
use App\Models\Service;

class ServiceCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Work Abroad',
                'description' => 'Comprehensive services for working opportunities abroad including visa processing, job placement, and documentation assistance.',
                'slug' => 'work-abroad',
                'icon' => 'fas fa-briefcase',
                'color_from' => 'blue-500',
                'color_to' => 'indigo-600',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Travel Services',
                'description' => 'Complete travel solutions including air ticketing, hotel bookings, and travel insurance.',
                'slug' => 'travel-services',
                'icon' => 'fas fa-plane',
                'color_from' => 'green-500',
                'color_to' => 'teal-600',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Study Abroad',
                'description' => 'Educational opportunities abroad with university admissions, student visa processing, and academic guidance.',
                'slug' => 'study-abroad',
                'icon' => 'fas fa-graduation-cap',
                'color_from' => 'purple-500',
                'color_to' => 'pink-600',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Visa Services',
                'description' => 'Professional visa processing services for various countries and visa types.',
                'slug' => 'visa-services',
                'icon' => 'fas fa-passport',
                'color_from' => 'indigo-500',
                'color_to' => 'purple-600',
                'sort_order' => 4,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            $category = ServiceCategory::create($categoryData);

            // Map existing services to categories based on their current category field
            $categoryMapping = [
                'work_abroad' => 'work-abroad',
                'travel' => 'travel-services',
                'study_abroad' => 'study-abroad',
                'visa' => 'visa-services',
            ];

            // Update services that match this category
            foreach ($categoryMapping as $oldCategory => $newSlug) {
                if ($category->slug === $newSlug) {
                    Service::where('category', $oldCategory)->update([
                        'service_category_id' => $category->id
                    ]);
                }
            }
        }

        // Handle any remaining services that don't match the mapping
        Service::whereNull('service_category_id')->update([
            'service_category_id' => ServiceCategory::where('slug', 'travel-services')->first()->id
        ]);
    }
}
