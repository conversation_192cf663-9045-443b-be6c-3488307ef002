<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            // Travel Services
            [
                'title' => 'Air Ticketing',
                'slug' => 'air-ticketing',
                'description' => 'Competitive rates for domestic and international flights with major airlines.',
                'features' => json_encode(['Domestic & international flights', 'Group booking discounts', '24/7 booking support']),
                'category' => 'travel',
                'color_from' => 'blue-500',
                'color_to' => 'indigo-600',
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'title' => 'Travel Packages',
                'slug' => 'travel-packages',
                'description' => 'Complete packages including accommodation, transport, and guided tours.',
                'features' => json_encode(['Safari packages', 'Beach holidays', 'Cultural tours']),
                'category' => 'travel',
                'color_from' => 'green-500',
                'color_to' => 'emerald-600',
                'sort_order' => 2,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'title' => 'Custom Itineraries',
                'slug' => 'custom-itineraries',
                'description' => 'Personalized travel plans tailored to your preferences and budget.',
                'features' => json_encode(['Personalized planning', 'Budget optimization', 'Local expertise']),
                'category' => 'travel',
                'color_from' => 'purple-500',
                'color_to' => 'fuchsia-600',
                'sort_order' => 3,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'title' => 'Business Travel',
                'slug' => 'business-travel',
                'description' => 'Professional travel services for corporate clients and business travelers.',
                'features' => json_encode(['Corporate accounts', 'Priority booking', '24/7 support']),
                'category' => 'travel',
                'color_from' => 'yellow-400',
                'color_to' => 'amber-500',
                'sort_order' => 4,
                'is_active' => true,
                'is_featured' => false,
            ],

            // Study Abroad Services
            [
                'title' => 'Study in USA',
                'slug' => 'study-in-usa',
                'description' => 'Access to top universities with comprehensive support throughout your journey.',
                'features' => json_encode(['University applications', 'Scholarship guidance', 'Visa assistance']),
                'category' => 'study_abroad',
                'color_from' => 'red-500',
                'color_to' => 'blue-600',
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'title' => 'Study in India',
                'slug' => 'study-in-india',
                'description' => 'Affordable education with excellent tech and medical programs.',
                'features' => json_encode(['Medical colleges', 'Engineering programs', 'Cost-effective education']),
                'category' => 'study_abroad',
                'color_from' => 'orange-500',
                'color_to' => 'green-600',
                'sort_order' => 2,
                'is_active' => true,
                'is_featured' => true,
            ],

            // Visa Services
            [
                'title' => 'Document Preparation',
                'slug' => 'document-preparation',
                'description' => 'Complete assistance with all required documentation for your visa application.',
                'features' => json_encode(['Document checklist', 'Form completion', 'Document review']),
                'category' => 'visa',
                'color_from' => 'indigo-500',
                'color_to' => 'purple-600',
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => true,
            ],
        ];

        foreach ($services as $service) {
            DB::table('services')->insert(array_merge($service, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}
