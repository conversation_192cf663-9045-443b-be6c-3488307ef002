<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $defaultSettings = [
            // Site Information
            ['key' => 'site_name', 'value' => 'Global Ventures Tanzania', 'label' => 'Site Name', 'group' => 'site', 'type' => 'text'],
            ['key' => 'site_tagline', 'value' => 'Your Gateway to Global Opportunities', 'label' => 'Site Tagline', 'group' => 'site', 'type' => 'text'],
            ['key' => 'site_description', 'value' => 'Global Ventures Tanzania is your trusted partner for travel, study abroad programs, and visa facilitation services.', 'label' => 'Site Description', 'group' => 'site', 'type' => 'textarea'],
            ['key' => 'site_url', 'value' => config('app.url'), 'label' => 'Site URL', 'group' => 'site', 'type' => 'text'],

            // Company Information
            ['key' => 'company_name', 'value' => 'Global Ventures Tanzania', 'label' => 'Company Name', 'group' => 'company', 'type' => 'text'],
            ['key' => 'company_address', 'value' => 'Victoria House Building, New Bagamoyo Road, 8th Floor, Wing B, Office 04', 'label' => 'Company Address', 'group' => 'company', 'type' => 'text'],
            ['key' => 'company_city', 'value' => 'Dar es Salaam', 'label' => 'Company City', 'group' => 'company', 'type' => 'text'],
            ['key' => 'company_country', 'value' => 'Tanzania', 'label' => 'Company Country', 'group' => 'company', 'type' => 'text'],

            // Contact Information
            ['key' => 'contact_email', 'value' => '<EMAIL>', 'label' => 'Contact Email', 'group' => 'contact', 'type' => 'text'],
            ['key' => 'contact_phone', 'value' => '+255 XXX XXX XXX', 'label' => 'Contact Phone', 'group' => 'contact', 'type' => 'text'],

            // Features
            ['key' => 'enable_blog', 'value' => '1', 'label' => 'Enable Blog', 'group' => 'features', 'type' => 'boolean'],
            ['key' => 'enable_testimonials', 'value' => '1', 'label' => 'Enable Testimonials', 'group' => 'features', 'type' => 'boolean'],
            ['key' => 'enable_contact_form', 'value' => '1', 'label' => 'Enable Contact Form', 'group' => 'features', 'type' => 'boolean'],
            ['key' => 'maintenance_mode', 'value' => '0', 'label' => 'Maintenance Mode', 'group' => 'system', 'type' => 'boolean'],
        ];

        foreach ($defaultSettings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
