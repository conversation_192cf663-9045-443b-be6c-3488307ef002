<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON><PERSON>',
                'position' => 'Computer Science Student',
                'company' => 'University of Toronto',
                'content' => 'Global Ventures Tanzania made my dream of studying in Canada a reality. Their team guided me through every step with professionalism and care.',
                'rating' => 5,
                'service_type' => 'study_abroad',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => '<PERSON>',
                'position' => 'Business Executive',
                'company' => 'Dar es Salaam',
                'content' => 'Exceptional travel services! They arranged my business trip to Dubai with perfect timing and great rates. Everything was seamless.',
                'rating' => 5,
                'service_type' => 'travel',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Fatuma <PERSON>',
                'position' => 'Medical Professional',
                'company' => 'Mwanza',
                'content' => 'Their visa facilitation service was outstanding. They handled all my documents for the US visa and I got approved on my first try!',
                'rating' => 5,
                'service_type' => 'visa',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'David Kimaro',
                'position' => 'Engineer',
                'company' => 'Arusha',
                'content' => 'I got my work visa for Dubai through Global Ventures. The process was smooth and they kept me informed every step of the way.',
                'rating' => 5,
                'service_type' => 'work_abroad',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Grace Mushi',
                'position' => 'Teacher',
                'company' => 'Moshi',
                'content' => 'Excellent service for my family vacation to India. They took care of everything from flights to accommodation. Highly recommended!',
                'rating' => 4,
                'service_type' => 'travel',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Hassan Ali',
                'position' => 'Student',
                'company' => 'University of Mumbai',
                'content' => 'Thanks to Global Ventures, I am now studying medicine in India. Their guidance throughout the admission process was invaluable.',
                'rating' => 5,
                'service_type' => 'study_abroad',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($testimonials as $testimonialData) {
            Testimonial::create($testimonialData);
        }
    }
}
