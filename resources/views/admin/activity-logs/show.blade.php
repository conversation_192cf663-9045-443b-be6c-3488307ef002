<x-admin-layout title="Activity Log Details">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.activity-logs.index') }}" class="hover:text-blue-600">Activity Logs</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Log #{{ $log->id }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Activity Log Details</h1>
                <p class="text-gray-600">Detailed information about this activity</p>
            </div>
            
            <div class="flex items-center space-x-3">
                <a href="{{ route('admin.activity-logs.index') }}" 
                   class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Logs
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Activity Overview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Activity Overview</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Action</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $log->action === 'created' ? 'bg-green-100 text-green-800' : 
                               ($log->action === 'updated' ? 'bg-yellow-100 text-yellow-800' : 
                                ($log->action === 'deleted' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800')) }}">
                            {{ ucfirst($log->action) }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Model Type</label>
                        @if($log->model_type)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                {{ class_basename($log->model_type) }}
                                @if($log->model_id)
                                    #{{ $log->model_id }}
                                @endif
                            </span>
                        @else
                            <span class="text-gray-500">System Action</span>
                        @endif
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <p class="text-gray-900">{{ $log->description }}</p>
                    </div>
                </div>
            </div>

            <!-- User Information -->
            @if($log->user)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">User Information</h3>
                    
                    <div class="flex items-center space-x-4">
                        <img src="{{ $log->user->avatar_url }}" alt="{{ $log->user->name }}" class="w-16 h-16 rounded-full">
                        
                        <div>
                            <h4 class="text-lg font-medium text-gray-900">{{ $log->user->name }}</h4>
                            <p class="text-gray-600">{{ $log->user->email }}</p>
                            @if($log->user->role)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mt-1">
                                    {{ $log->user->role->display_name }}
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Technical Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Technical Details</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">IP Address</label>
                        <p class="text-gray-900 font-mono">{{ $log->ip_address ?: 'N/A' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Log ID</label>
                        <p class="text-gray-900 font-mono">#{{ $log->id }}</p>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">User Agent</label>
                        <p class="text-gray-900 text-sm break-all">{{ $log->user_agent ?: 'N/A' }}</p>
                    </div>
                </div>
            </div>

            <!-- Data Changes -->
            @if($log->old_values || $log->new_values)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Data Changes</h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Old Values -->
                        @if($log->old_values && count($log->old_values) > 0)
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-3">
                                    <i class="fas fa-minus-circle text-red-500 mr-2"></i>
                                    Before Changes
                                </h4>
                                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                    <pre class="text-sm text-gray-800 whitespace-pre-wrap">{{ json_encode($log->old_values, JSON_PRETTY_PRINT) }}</pre>
                                </div>
                            </div>
                        @endif

                        <!-- New Values -->
                        @if($log->new_values && count($log->new_values) > 0)
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-3">
                                    <i class="fas fa-plus-circle text-green-500 mr-2"></i>
                                    After Changes
                                </h4>
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <pre class="text-sm text-gray-800 whitespace-pre-wrap">{{ json_encode($log->new_values, JSON_PRETTY_PRINT) }}</pre>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Timestamp Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Timestamp</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Date & Time</label>
                        <p class="text-gray-900">{{ $log->created_at->format('M d, Y H:i:s') }}</p>
                        <p class="text-gray-500">{{ $log->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Timezone</label>
                        <p class="text-gray-900">{{ $log->created_at->format('T') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Unix Timestamp</label>
                        <p class="text-gray-900 font-mono">{{ $log->created_at->timestamp }}</p>
                    </div>
                </div>
            </div>

            <!-- Action Summary -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Action Summary</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Action Type</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $log->action === 'created' ? 'bg-green-100 text-green-800' : 
                               ($log->action === 'updated' ? 'bg-yellow-100 text-yellow-800' : 
                                ($log->action === 'deleted' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800')) }}">
                            {{ ucfirst($log->action) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Model</span>
                        <span class="text-sm font-medium text-gray-900">
                            {{ $log->model_type ? class_basename($log->model_type) : 'System' }}
                        </span>
                    </div>
                    
                    @if($log->model_id)
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Model ID</span>
                            <span class="text-sm font-medium text-gray-900">#{{ $log->model_id }}</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Has Changes</span>
                        <span class="text-sm font-medium text-gray-900">
                            {{ ($log->old_values || $log->new_values) ? 'Yes' : 'No' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Related Model -->
            @if($log->model_type && $log->model_id)
                <div class="bg-white rounded-lg shadow p-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Related Model</h4>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-database text-blue-600 text-xl"></i>
                        </div>
                        
                        <h5 class="font-medium text-gray-900">{{ class_basename($log->model_type) }}</h5>
                        <p class="text-sm text-gray-600">ID: {{ $log->model_id }}</p>
                        
                        @if($log->model())
                            <p class="text-xs text-green-600 mt-2">
                                <i class="fas fa-check-circle mr-1"></i>
                                Model exists
                            </p>
                        @else
                            <p class="text-xs text-red-600 mt-2">
                                <i class="fas fa-times-circle mr-1"></i>
                                Model deleted
                            </p>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.activity-logs.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Activity Logs
                    </a>
                    
                    @if($log->user)
                        <a href="{{ route('admin.activity-logs.index', ['user_id' => $log->user->id]) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-user mr-2"></i>
                            User's Activities
                        </a>
                    @endif
                    
                    @if($log->model_type)
                        <a href="{{ route('admin.activity-logs.index', ['model_type' => $log->model_type]) }}" 
                           class="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center block">
                            <i class="fas fa-database mr-2"></i>
                            Model Activities
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
