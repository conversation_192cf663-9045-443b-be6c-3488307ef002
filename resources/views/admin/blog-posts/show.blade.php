<x-admin-layout title="View Blog Post">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.blog-posts.index') }}" class="hover:text-blue-600">Blog Posts</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ Str::limit($blogPost->title, 30) }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $blogPost->title }}</h1>
                <p class="text-gray-600">Blog post details and content</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_blog'))
                    <a href="{{ route('admin.blog-posts.edit', $blogPost) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Post
                    </a>
                @endif
                
                @if(auth()->user()->hasPermission('delete_blog'))
                    <form method="POST" action="{{ route('admin.blog-posts.destroy', $blogPost) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this blog post?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Featured Image -->
            @if($blogPost->featured_image)
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <img src="{{ Storage::url($blogPost->featured_image) }}" alt="{{ $blogPost->title }}" class="w-full h-64 object-cover">
                </div>
            @endif

            <!-- Post Content -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Post Content</h3>
                    
                    <div class="flex items-center space-x-2">
                        @if($blogPost->is_featured)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-star mr-1"></i>
                                Featured
                            </span>
                        @endif
                        
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $blogPost->status === 'published' ? 'bg-green-100 text-green-800' : 
                               ($blogPost->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ ucfirst($blogPost->status) }}
                        </span>
                    </div>
                </div>
                
                @if($blogPost->excerpt)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Excerpt</h4>
                        <p class="text-gray-600 italic">{{ $blogPost->excerpt }}</p>
                    </div>
                @endif
                
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Content</h4>
                    <div class="prose max-w-none">
                        {!! nl2br(e($blogPost->content)) !!}
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            @if($blogPost->meta_title || $blogPost->meta_description)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">SEO Information</h3>
                    
                    @if($blogPost->meta_title)
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Title</label>
                            <p class="text-gray-900">{{ $blogPost->meta_title }}</p>
                            <p class="text-xs text-gray-500">{{ strlen($blogPost->meta_title) }} characters</p>
                        </div>
                    @endif
                    
                    @if($blogPost->meta_description)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Meta Description</label>
                            <p class="text-gray-900">{{ $blogPost->meta_description }}</p>
                            <p class="text-xs text-gray-500">{{ strlen($blogPost->meta_description) }} characters</p>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Tags -->
            @if($blogPost->tags)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                    
                    <div class="flex flex-wrap gap-2">
                        @if(is_array($blogPost->tags))
                            @foreach($blogPost->tags as $tag)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    #{{ trim($tag) }}
                                </span>
                            @endforeach
                        @else
                            @foreach(explode(',', $blogPost->tags) as $tag)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    #{{ trim($tag) }}
                                </span>
                            @endforeach
                        @endif
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Post Status -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Post Status</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Status</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $blogPost->status === 'published' ? 'bg-green-100 text-green-800' : 
                               ($blogPost->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800') }}">
                            {{ ucfirst($blogPost->status) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Featured</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $blogPost->is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $blogPost->is_featured ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    
                    @if($blogPost->category)
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Category</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ ucfirst(str_replace('_', ' ', $blogPost->category)) }}
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Author Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Author</h4>
                
                <div class="flex items-center space-x-3">
                    <img src="{{ $blogPost->author->avatar_url }}" alt="{{ $blogPost->author->name }}" class="w-12 h-12 rounded-full">
                    <div>
                        <p class="font-medium text-gray-900">{{ $blogPost->author->name }}</p>
                        <p class="text-sm text-gray-600">{{ $blogPost->author->email }}</p>
                        @if($blogPost->author->role)
                            <p class="text-xs text-gray-500">{{ $blogPost->author->role->display_name }}</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Publishing Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Publishing</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Created</label>
                        <p class="text-gray-900">{{ $blogPost->created_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $blogPost->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $blogPost->updated_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $blogPost->updated_at->diffForHumans() }}</p>
                    </div>
                    
                    @if($blogPost->published_at)
                        <div>
                            <label class="block text-gray-600 mb-1">Published</label>
                            <p class="text-gray-900">{{ $blogPost->published_at->format('M d, Y H:i') }}</p>
                            <p class="text-gray-500">{{ $blogPost->published_at->diffForHumans() }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Post ID</label>
                        <p class="text-gray-900 font-mono">#{{ $blogPost->id }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Slug</label>
                        <p class="text-gray-900 font-mono text-xs">{{ $blogPost->slug }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_blog'))
                        <a href="{{ route('admin.blog-posts.edit', $blogPost) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Post
                        </a>
                        
                        @if($blogPost->status === 'draft')
                            <button onclick="publishPost({{ $blogPost->id }})" 
                                    class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Publish Post
                            </button>
                        @elseif($blogPost->status === 'published')
                            <button onclick="unpublishPost({{ $blogPost->id }})" 
                                    class="w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                                <i class="fas fa-pause mr-2"></i>
                                Unpublish Post
                            </button>
                        @endif
                    @endif
                    
                    <a href="{{ route('admin.blog-posts.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Posts
                    </a>
                    
                    @if(auth()->user()->hasPermission('create_blog'))
                        <a href="{{ route('admin.blog-posts.create') }}" 
                           class="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            New Post
                        </a>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Content Length</span>
                        <span class="font-medium text-gray-900">{{ number_format(strlen($blogPost->content)) }} chars</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Word Count</span>
                        <span class="font-medium text-gray-900">{{ str_word_count(strip_tags($blogPost->content)) }} words</span>
                    </div>
                    
                    @if($blogPost->tags)
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Tags Count</span>
                            <span class="font-medium text-gray-900">{{ is_array($blogPost->tags) ? count($blogPost->tags) : count(explode(',', $blogPost->tags)) }}</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Reading Time</span>
                        <span class="font-medium text-gray-900">{{ ceil(str_word_count(strip_tags($blogPost->content)) / 200) }} min</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <x-slot name="scripts">
        <script>
            function publishPost(postId) {
                if (!confirm('Are you sure you want to publish this post?')) {
                    return;
                }

                fetch(`/admin/blog-posts/${postId}/publish`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'Failed to publish post');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to publish post');
                });
            }

            function unpublishPost(postId) {
                if (!confirm('Are you sure you want to unpublish this post?')) {
                    return;
                }

                fetch(`/admin/blog-posts/${postId}/unpublish`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'Failed to unpublish post');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to unpublish post');
                });
            }
        </script>
    </x-slot>
</x-admin-layout>
