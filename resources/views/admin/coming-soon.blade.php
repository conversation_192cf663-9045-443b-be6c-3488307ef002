<x-admin-layout :title="$feature">
    <div class="text-center py-16">
        <div class="max-w-md mx-auto">
            <div class="mb-8">
                <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-tools text-3xl text-blue-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ $feature }}</h1>
                <p class="text-gray-600">This feature is coming soon!</p>
            </div>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-blue-900 mb-2">What's Available Now</h3>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li>✅ Dashboard with Analytics</li>
                    <li>✅ Services Management (Full CRUD)</li>
                    <li>✅ Role-based Access Control</li>
                    <li>✅ Activity Logging</li>
                    <li>✅ File Upload System</li>
                </ul>
            </div>
            
            <div class="space-y-4">
                <a href="{{ route('admin.dashboard') }}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
                
                <div class="text-sm text-gray-500">
                    <p>The admin panel foundation is complete and ready for extension.</p>
                    <p>Additional features can be easily added following the established patterns.</p>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
