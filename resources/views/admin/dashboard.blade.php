<x-admin-layout title="Dashboard">
    <!-- Dashboard Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-users text-2xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $stats['total_users'] }}</h3>
                    <p class="text-sm text-gray-600">Total Users</p>
                    @if($stats['new_users_today'] > 0)
                        <p class="text-xs text-green-600">+{{ $stats['new_users_today'] }} today</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Blog Posts -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-blog text-2xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $stats['published_posts'] }}</h3>
                    <p class="text-sm text-gray-600">Published Posts</p>
                    @if($stats['draft_posts'] > 0)
                        <p class="text-xs text-yellow-600">{{ $stats['draft_posts'] }} drafts</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Services -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-concierge-bell text-2xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $stats['active_services'] }}</h3>
                    <p class="text-sm text-gray-600">Active Services</p>
                    <p class="text-xs text-gray-500">{{ $stats['total_services'] }} total</p>
                </div>
            </div>
        </div>

        <!-- Contact Inquiries -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-envelope text-2xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $stats['new_inquiries'] }}</h3>
                    <p class="text-sm text-gray-600">New Inquiries</p>
                    @if($stats['inquiries_today'] > 0)
                        <p class="text-xs text-blue-600">+{{ $stats['inquiries_today'] }} today</p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity Overview (Last 30 Days)</h3>
            <canvas id="activityChart" width="400" height="200"></canvas>
        </div>

        <!-- Recent Inquiries -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Inquiries</h3>
                @if(auth()->user()->hasPermission('view_inquiries'))
                    <a href="{{ route('admin.inquiries.index') }}" class="text-blue-600 hover:text-blue-700 text-sm">View All</a>
                @endif
            </div>
            
            @if($recentInquiries->count() > 0)
                <div class="space-y-4">
                    @foreach($recentInquiries as $inquiry)
                        <div class="border-l-4 border-{{ $inquiry->status_color }}-400 pl-4">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-900">{{ $inquiry->name }}</h4>
                                <span class="text-xs text-gray-500">{{ $inquiry->created_at->diffForHumans() }}</span>
                            </div>
                            <p class="text-sm text-gray-600">{{ $inquiry->subject }}</p>
                            <div class="flex items-center mt-1">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-{{ $inquiry->status_color }}-100 text-{{ $inquiry->status_color }}-800">
                                    {{ ucfirst(str_replace('_', ' ', $inquiry->status)) }}
                                </span>
                                @if($inquiry->service_interest)
                                    <span class="ml-2 text-xs text-gray-500">{{ $inquiry->service_interest }}</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-8">No recent inquiries</p>
            @endif
        </div>
    </div>

    <!-- Recent Activities -->
    @if(auth()->user()->hasPermission('view_logs'))
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Activities</h3>
                <a href="{{ route('admin.activity-logs.index') }}" class="text-blue-600 hover:text-blue-700 text-sm">View All</a>
            </div>
            
            @if($recentActivities->count() > 0)
                <div class="space-y-3">
                    @foreach($recentActivities as $activity)
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-{{ $activity->action_color }}-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-{{ $activity->action === 'created' ? 'plus' : ($activity->action === 'updated' ? 'edit' : ($activity->action === 'deleted' ? 'trash' : 'eye')) }} text-{{ $activity->action_color }}-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm text-gray-900">
                                    <span class="font-medium">{{ $activity->user->name }}</span>
                                    {{ $activity->description }}
                                </p>
                                <p class="text-xs text-gray-500">{{ $activity->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-8">No recent activities</p>
            @endif
        </div>
    @endif

    <x-slot name="scripts">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            // Activity Chart
            const ctx = document.getElementById('activityChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: @json($chartData['labels']),
                    datasets: [{
                        label: 'Inquiries',
                        data: @json($chartData['inquiries']),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    }, {
                        label: 'New Users',
                        data: @json($chartData['users']),
                        borderColor: 'rgb(16, 185, 129)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        </script>
    </x-slot>
</x-admin-layout>
