<x-admin-layout title="Create FAQ">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.faqs.index') }}" class="hover:text-blue-600">FAQs</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Create FAQ</span>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900">Create New FAQ</h1>
        <p class="text-gray-600">Add a new frequently asked question</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow p-6">
                <form method="POST" action="{{ route('admin.faqs.store') }}" class="space-y-6">
                    @csrf

                    <!-- Question -->
                    <div>
                        <label for="question" class="block text-sm font-medium text-gray-700 mb-2">
                            Question <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="question" 
                               name="question" 
                               value="{{ old('question') }}" 
                               required
                               maxlength="500"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('question') border-red-500 @enderror">
                        @error('question')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Maximum 500 characters</p>
                    </div>

                    <!-- Answer -->
                    <div>
                        <label for="answer" class="block text-sm font-medium text-gray-700 mb-2">
                            Answer <span class="text-red-500">*</span>
                        </label>
                        <textarea id="answer" 
                                  name="answer" 
                                  rows="8"
                                  required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('answer') border-red-500 @enderror">{{ old('answer') }}</textarea>
                        @error('answer')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Provide a detailed answer to the question</p>
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                            Category
                        </label>
                        <select id="category" 
                                name="category"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('category') border-red-500 @enderror">
                            <option value="">Select Category</option>
                            <option value="general" {{ old('category') === 'general' ? 'selected' : '' }}>General</option>
                            <option value="travel" {{ old('category') === 'travel' ? 'selected' : '' }}>Travel</option>
                            <option value="visa" {{ old('category') === 'visa' ? 'selected' : '' }}>Visa Services</option>
                            <option value="study_abroad" {{ old('category') === 'study_abroad' ? 'selected' : '' }}>Study Abroad</option>
                            <option value="documentation" {{ old('category') === 'documentation' ? 'selected' : '' }}>Documentation</option>
                            <option value="payment" {{ old('category') === 'payment' ? 'selected' : '' }}>Payment & Pricing</option>
                            <option value="support" {{ old('category') === 'support' ? 'selected' : '' }}>Support</option>
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Categorize this FAQ for better organization</p>
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            Sort Order
                        </label>
                        <input type="number" 
                               id="sort_order" 
                               name="sort_order" 
                               value="{{ old('sort_order', 0) }}"
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('sort_order') border-red-500 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Lower numbers appear first (0 = highest priority)</p>
                    </div>

                    <!-- Status -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   {{ old('is_active', true) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active FAQ
                            </label>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Only active FAQs will be displayed on the website</p>
                    </div>

                    <!-- Actions -->
                    <div class="border-t border-gray-200 pt-6 flex items-center justify-between">
                        <a href="{{ route('admin.faqs.index') }}" 
                           class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Cancel
                        </a>
                        
                        <button type="submit" 
                                class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>
                            Create FAQ
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Tips -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Writing Tips</h3>
                
                <div class="space-y-3 text-sm text-gray-600">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Keep questions clear and specific to what users actually ask</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Provide comprehensive answers that address all aspects of the question</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Use simple language and avoid technical jargon when possible</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Include relevant links or contact information when helpful</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Organize FAQs by category for easier navigation</p>
                    </div>
                </div>
            </div>

            <!-- Categories Guide -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Category Guide</h3>
                
                <div class="space-y-2 text-sm">
                    <div>
                        <span class="font-medium text-gray-900">General:</span>
                        <span class="text-gray-600">Basic company and service information</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-900">Travel:</span>
                        <span class="text-gray-600">Travel planning and booking questions</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-900">Visa:</span>
                        <span class="text-gray-600">Visa application and processing</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-900">Study Abroad:</span>
                        <span class="text-gray-600">Educational programs and requirements</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-900">Documentation:</span>
                        <span class="text-gray-600">Required documents and paperwork</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-900">Payment:</span>
                        <span class="text-gray-600">Pricing, payment methods, and refunds</span>
                    </div>
                    
                    <div>
                        <span class="font-medium text-gray-900">Support:</span>
                        <span class="text-gray-600">Customer service and contact information</span>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="mb-2">
                        <span id="preview-category" class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            General
                        </span>
                    </div>
                    
                    <h4 id="preview-question" class="font-medium text-gray-900 mb-2">
                        Your question will appear here...
                    </h4>
                    
                    <p id="preview-answer" class="text-gray-600 text-sm">
                        Your answer will appear here...
                    </p>
                </div>
            </div>
        </div>
    </div>

    <x-slot name="scripts">
        <script>
            // Live preview
            function updatePreview() {
                const question = document.getElementById('question').value || 'Your question will appear here...';
                const answer = document.getElementById('answer').value || 'Your answer will appear here...';
                const category = document.getElementById('category').value || 'general';
                
                document.getElementById('preview-question').textContent = question;
                document.getElementById('preview-answer').textContent = answer;
                document.getElementById('preview-category').textContent = category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ');
            }

            // Add event listeners
            document.getElementById('question').addEventListener('input', updatePreview);
            document.getElementById('answer').addEventListener('input', updatePreview);
            document.getElementById('category').addEventListener('change', updatePreview);

            // Character counter for question
            document.getElementById('question').addEventListener('input', function() {
                const length = this.value.length;
                const maxLength = 500;
                const helpText = this.parentNode.querySelector('.text-gray-500');
                
                helpText.textContent = `${length}/${maxLength} characters`;
                
                if (length > maxLength * 0.9) {
                    helpText.classList.add('text-red-600');
                    helpText.classList.remove('text-gray-500');
                } else {
                    helpText.classList.remove('text-red-600');
                    helpText.classList.add('text-gray-500');
                }
            });
        </script>
    </x-slot>
</x-admin-layout>
