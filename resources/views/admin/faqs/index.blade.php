<x-admin-layout title="FAQs">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Frequently Asked Questions</h1>
                <p class="text-gray-600">Manage your FAQ content and organization</p>
            </div>
            
            @if(auth()->user()->hasPermission('create_faqs'))
                <a href="{{ route('admin.faqs.create') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    New FAQ
                </a>
            @endif
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <form method="GET" action="{{ route('admin.faqs.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" 
                       id="search" 
                       name="search" 
                       value="{{ request('search') }}"
                       placeholder="Search questions or answers..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Category -->
            <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select id="category" 
                        name="category"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Categories</option>
                    @foreach($categories as $cat)
                        <option value="{{ $cat }}" {{ request('category') === $cat ? 'selected' : '' }}>
                            {{ ucfirst(str_replace('_', ' ', $cat)) }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select id="status" 
                        name="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Statuses</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <!-- Actions -->
            <div class="flex items-end space-x-2">
                <button type="submit" 
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    Filter
                </button>
                
                <a href="{{ route('admin.faqs.index') }}" 
                   class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-question-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total FAQs</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $faqs->total() }}</h3>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $faqs->where('is_active', true)->count() }}</h3>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-times-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Inactive</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $faqs->where('is_active', false)->count() }}</h3>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-tags text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Categories</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $categories->count() }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQs List -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        @if($faqs->count() > 0)
            <div class="divide-y divide-gray-200" id="faqs-container">
                @foreach($faqs as $faq)
                    <div class="p-6 hover:bg-gray-50 transition-colors" data-faq-id="{{ $faq->id }}">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-2">
                                    @if(auth()->user()->hasPermission('edit_faqs'))
                                        <div class="cursor-move text-gray-400 hover:text-gray-600">
                                            <i class="fas fa-grip-vertical"></i>
                                        </div>
                                    @endif
                                    
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            {{ $faq->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $faq->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                        
                                        @if($faq->category)
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ ucfirst(str_replace('_', ' ', $faq->category)) }}
                                            </span>
                                        @endif
                                        
                                        <span class="text-xs text-gray-500">Order: {{ $faq->sort_order }}</span>
                                    </div>
                                </div>
                                
                                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $faq->question }}</h3>
                                <p class="text-gray-600 leading-relaxed">{{ Str::limit($faq->answer, 200) }}</p>
                                
                                <div class="mt-3 text-xs text-gray-500">
                                    Created {{ $faq->created_at->diffForHumans() }}
                                    @if($faq->updated_at != $faq->created_at)
                                        • Updated {{ $faq->updated_at->diffForHumans() }}
                                    @endif
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-2 ml-4">
                                @if(auth()->user()->hasPermission('view_faqs'))
                                    <a href="{{ route('admin.faqs.show', $faq) }}" 
                                       class="text-blue-600 hover:text-blue-900" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                @endif
                                
                                @if(auth()->user()->hasPermission('edit_faqs'))
                                    <a href="{{ route('admin.faqs.edit', $faq) }}" 
                                       class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                @endif
                                
                                @if(auth()->user()->hasPermission('delete_faqs'))
                                    <form method="POST" action="{{ route('admin.faqs.destroy', $faq) }}" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to delete this FAQ?')"
                                                class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($faqs->hasPages())
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    {{ $faqs->links() }}
                </div>
            @endif
        @else
            <div class="p-12 text-center">
                <div class="flex flex-col items-center">
                    <i class="fas fa-question-circle text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No FAQs found</h3>
                    <p class="text-gray-500 mb-4">Get started by creating your first FAQ.</p>
                    
                    @if(auth()->user()->hasPermission('create_faqs'))
                        <a href="{{ route('admin.faqs.create') }}" 
                           class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Create FAQ
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>

    <x-slot name="scripts">
        @if(auth()->user()->hasPermission('edit_faqs') && $faqs->count() > 0)
            <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const container = document.getElementById('faqs-container');
                    
                    if (container) {
                        const sortable = Sortable.create(container, {
                            handle: '.cursor-move',
                            animation: 150,
                            onEnd: function(evt) {
                                const faqs = [];
                                const items = container.children;
                                
                                for (let i = 0; i < items.length; i++) {
                                    const faqId = items[i].getAttribute('data-faq-id');
                                    faqs.push({
                                        id: parseInt(faqId),
                                        sort_order: i
                                    });
                                }
                                
                                fetch('{{ route("admin.faqs.reorder") }}', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                    },
                                    body: JSON.stringify({ faqs: faqs })
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (!data.success) {
                                        alert('Failed to reorder FAQs');
                                        location.reload();
                                    }
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    alert('Failed to reorder FAQs');
                                    location.reload();
                                });
                            }
                        });
                    }
                });
            </script>
        @endif
    </x-slot>
</x-admin-layout>
