<x-admin-layout title="View FAQ">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.faqs.index') }}" class="hover:text-blue-600">FAQs</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ Str::limit($faq->question, 30) }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">FAQ Details</h1>
                <p class="text-gray-600">View FAQ question and answer</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_faqs'))
                    <a href="{{ route('admin.faqs.edit', $faq) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit FAQ
                    </a>
                @endif
                
                @if(auth()->user()->hasPermission('delete_faqs'))
                    <form method="POST" action="{{ route('admin.faqs.destroy', $faq) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this FAQ?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- FAQ Display -->
            <div class="bg-white rounded-lg shadow p-8">
                <div class="mb-6">
                    <div class="flex items-center space-x-2 mb-4">
                        @if($faq->category)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                {{ ucfirst(str_replace('_', ' ', $faq->category)) }}
                            </span>
                        @endif
                        
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $faq->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $faq->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        
                        <span class="text-sm text-gray-500">Order: {{ $faq->sort_order }}</span>
                    </div>
                    
                    <!-- Question -->
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">
                        <i class="fas fa-question-circle text-blue-600 mr-3"></i>
                        {{ $faq->question }}
                    </h2>
                    
                    <!-- Answer -->
                    <div class="bg-blue-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-blue-900 mb-4">
                            <i class="fas fa-lightbulb mr-2"></i>
                            Answer
                        </h3>
                        <div class="prose max-w-none text-gray-700 leading-relaxed">
                            {!! nl2br(e($faq->answer)) !!}
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">FAQ Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Question</label>
                        <p class="text-gray-900">{{ $faq->question }}</p>
                    </div>
                    
                    @if($faq->category)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ ucfirst(str_replace('_', ' ', $faq->category)) }}
                            </span>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                        <p class="text-gray-900">{{ $faq->sort_order }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $faq->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $faq->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
                
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Answer</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 leading-relaxed whitespace-pre-wrap">{{ $faq->answer }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Status</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Active</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $faq->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $faq->is_active ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    
                    @if($faq->category)
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Category</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ ucfirst(str_replace('_', ' ', $faq->category)) }}
                            </span>
                        </div>
                    @endif
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Sort Order</span>
                        <span class="text-sm font-medium text-gray-900">{{ $faq->sort_order }}</span>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Metadata</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Created</label>
                        <p class="text-gray-900">{{ $faq->created_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $faq->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $faq->updated_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $faq->updated_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">FAQ ID</label>
                        <p class="text-gray-900 font-mono">#{{ $faq->id }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_faqs'))
                        <a href="{{ route('admin.faqs.edit', $faq) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit FAQ
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.faqs.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All FAQs
                    </a>
                    
                    @if(auth()->user()->hasPermission('create_faqs'))
                        <a href="{{ route('admin.faqs.create') }}" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            New FAQ
                        </a>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Question Length</span>
                        <span class="font-medium text-gray-900">{{ strlen($faq->question) }} chars</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Answer Length</span>
                        <span class="font-medium text-gray-900">{{ strlen($faq->answer) }} chars</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Answer Word Count</span>
                        <span class="font-medium text-gray-900">{{ str_word_count($faq->answer) }} words</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Reading Time</span>
                        <span class="font-medium text-gray-900">{{ ceil(str_word_count($faq->answer) / 200) }} min</span>
                    </div>
                    
                    @if($faq->category)
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Category FAQs</span>
                            <span class="font-medium text-gray-900">
                                {{ \App\Models\Faq::where('category', $faq->category)->count() }}
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Related FAQs -->
            @if($faq->category)
                @php
                    $relatedFaqs = \App\Models\Faq::where('category', $faq->category)
                        ->where('id', '!=', $faq->id)
                        ->where('is_active', true)
                        ->orderBy('sort_order')
                        ->limit(5)
                        ->get();
                @endphp
                
                @if($relatedFaqs->count() > 0)
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Related FAQs</h4>
                        
                        <div class="space-y-3">
                            @foreach($relatedFaqs as $relatedFaq)
                                <div class="border-l-4 border-blue-500 pl-3">
                                    <a href="{{ route('admin.faqs.show', $relatedFaq) }}" 
                                       class="text-sm font-medium text-blue-600 hover:text-blue-800">
                                        {{ Str::limit($relatedFaq->question, 60) }}
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            @endif
        </div>
    </div>
</x-admin-layout>
