<x-admin-layout title="Feedback Details">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.feedback.index') }}" class="hover:text-blue-600">Feedback</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ Str::limit($feedback->subject, 30) }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $feedback->subject }}</h1>
                <p class="text-gray-600">Feedback from {{ $feedback->name }}</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_feedback'))
                    <button onclick="togglePublic({{ $feedback->id }})" 
                            class="bg-{{ $feedback->is_public ? 'green' : 'gray' }}-600 text-white px-4 py-2 rounded-lg hover:bg-{{ $feedback->is_public ? 'green' : 'gray' }}-700 transition-colors">
                        <i class="fas fa-{{ $feedback->is_public ? 'eye' : 'eye-slash' }} mr-2"></i>
                        {{ $feedback->is_public ? 'Public' : 'Private' }}
                    </button>
                @endif
                
                @if(auth()->user()->hasPermission('delete_feedback'))
                    <form method="POST" action="{{ route('admin.feedback.destroy', $feedback) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this feedback?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Feedback Content -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Feedback Details</h3>
                    
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $feedback->type_color }}-100 text-{{ $feedback->type_color }}-800">
                            {{ ucfirst($feedback->type) }}
                        </span>
                        
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $feedback->status_color }}-100 text-{{ $feedback->status_color }}-800">
                            {{ ucfirst(str_replace('_', ' ', $feedback->status)) }}
                        </span>
                        
                        @if($feedback->is_public)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-eye mr-1"></i>
                                Public
                            </span>
                        @endif
                    </div>
                </div>
                
                @if($feedback->rating)
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Rating</h4>
                        <div class="flex items-center space-x-2">
                            <div class="flex text-yellow-400">
                                {!! $feedback->star_rating !!}
                            </div>
                            <span class="text-sm text-gray-600">({{ $feedback->rating }}/5)</span>
                        </div>
                    </div>
                @endif
                
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Message</h4>
                    <div class="prose max-w-none">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ $feedback->message }}</p>
                    </div>
                </div>
            </div>

            <!-- Admin Notes -->
            @if(auth()->user()->hasPermission('edit_feedback'))
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Admin Notes</h3>
                    
                    <form method="POST" action="{{ route('admin.feedback.update', $feedback) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="mb-4">
                            <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                            <textarea id="admin_notes" name="admin_notes" rows="4" 
                                      class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="Add internal notes about this feedback...">{{ old('admin_notes', $feedback->admin_notes) }}</textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select id="status" name="status" class="w-full border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="new" {{ $feedback->status === 'new' ? 'selected' : '' }}>New</option>
                                    <option value="in_progress" {{ $feedback->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                    <option value="resolved" {{ $feedback->status === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                    <option value="closed" {{ $feedback->status === 'closed' ? 'selected' : '' }}>Closed</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Visibility</label>
                                <div class="flex items-center">
                                    <input type="checkbox" id="is_public" name="is_public" value="1" 
                                           {{ $feedback->is_public ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="is_public" class="ml-2 block text-sm text-gray-900">
                                        Make this feedback public (visible on testimonials page)
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-save mr-2"></i>
                            Update Feedback
                        </button>
                    </form>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h4>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-600 mb-1">Name</label>
                        <p class="text-gray-900 font-medium">{{ $feedback->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Email</label>
                        <p class="text-gray-900">
                            <a href="mailto:{{ $feedback->email }}" class="text-blue-600 hover:text-blue-800">
                                {{ $feedback->email }}
                            </a>
                        </p>
                    </div>
                    
                    @if($feedback->phone)
                        <div>
                            <label class="block text-gray-600 mb-1">Phone</label>
                            <p class="text-gray-900">
                                <a href="tel:{{ $feedback->phone }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $feedback->phone }}
                                </a>
                            </p>
                        </div>
                    @endif
                    
                    @if($feedback->service_type)
                        <div>
                            <label class="block text-gray-600 mb-1">Service Type</label>
                            <p class="text-gray-900">{{ ucfirst(str_replace('_', ' ', $feedback->service_type)) }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Feedback Metadata -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Feedback Details</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Submitted</span>
                        <span class="font-medium text-gray-900">{{ $feedback->created_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                    
                    @if($feedback->responded_at)
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Responded</span>
                            <span class="font-medium text-gray-900">{{ $feedback->responded_at->format('M d, Y \a\t g:i A') }}</span>
                        </div>
                    @endif
                    
                    @if($feedback->respondedBy)
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Responded By</span>
                            <span class="font-medium text-gray-900">{{ $feedback->respondedBy->name }}</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Feedback ID</span>
                        <span class="font-medium text-gray-900 font-mono">#{{ $feedback->id }}</span>
                    </div>
                    
                    @if($feedback->ip_address)
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">IP Address</span>
                            <span class="font-medium text-gray-900 font-mono text-xs">{{ $feedback->ip_address }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    <a href="mailto:{{ $feedback->email }}?subject=Re: {{ $feedback->subject }}" 
                       class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                        <i class="fas fa-reply mr-2"></i>
                        Reply via Email
                    </a>
                    
                    <a href="{{ route('admin.feedback.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Feedback
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if(auth()->user()->hasPermission('edit_feedback'))
        <script>
            function togglePublic(feedbackId) {
                fetch(`/admin/feedback/${feedbackId}/toggle-public`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to update visibility');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred');
                });
            }
        </script>
    @endif
</x-admin-layout>
