<x-admin-layout title="View Inquiry">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.inquiries.index') }}" class="hover:text-blue-600">Contact Inquiries</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ Str::limit($inquiry->subject, 30) }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $inquiry->subject }}</h1>
                <p class="text-gray-600">Contact inquiry from {{ $inquiry->name }}</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('delete_inquiries'))
                    <form method="POST" action="{{ route('admin.inquiries.destroy', $inquiry) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this inquiry?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Inquiry Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Inquiry Details</h3>
                    
                    <div class="flex items-center space-x-2">
                        @if(!$inquiry->is_read)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-circle mr-1 text-xs"></i>
                                Unread
                            </span>
                        @endif
                        
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $inquiry->status === 'new' ? 'bg-yellow-100 text-yellow-800' : 
                               ($inquiry->status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 
                                ($inquiry->status === 'resolved' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')) }}">
                            {{ ucfirst(str_replace('_', ' ', $inquiry->status)) }}
                        </span>
                        
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $inquiry->priority === 'urgent' ? 'bg-red-100 text-red-800' : 
                               ($inquiry->priority === 'high' ? 'bg-orange-100 text-orange-800' : 
                                ($inquiry->priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                            {{ ucfirst($inquiry->priority) }} Priority
                        </span>
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <p class="text-gray-900">{{ $inquiry->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <p class="text-gray-900">
                            <a href="mailto:{{ $inquiry->email }}" class="text-blue-600 hover:text-blue-800">
                                {{ $inquiry->email }}
                            </a>
                        </p>
                    </div>
                    
                    @if($inquiry->phone)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                            <p class="text-gray-900">
                                <a href="tel:{{ $inquiry->phone }}" class="text-blue-600 hover:text-blue-800">
                                    {{ $inquiry->phone }}
                                </a>
                            </p>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                        <p class="text-gray-900">{{ $inquiry->subject }}</p>
                    </div>
                </div>
                
                <!-- Message -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 leading-relaxed whitespace-pre-wrap">{{ $inquiry->message }}</p>
                    </div>
                </div>
                
                <!-- Notes -->
                @if($inquiry->notes)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Internal Notes</label>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <p class="text-gray-900 leading-relaxed whitespace-pre-wrap">{{ $inquiry->notes }}</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Update Status -->
            @if(auth()->user()->hasPermission('edit_inquiries'))
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Update Inquiry</h3>
                    
                    <form method="POST" action="{{ route('admin.inquiries.update', $inquiry) }}" class="space-y-6">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Status -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Status <span class="text-red-500">*</span>
                                </label>
                                <select id="status" 
                                        name="status" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="new" {{ $inquiry->status === 'new' ? 'selected' : '' }}>New</option>
                                    <option value="in_progress" {{ $inquiry->status === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                    <option value="resolved" {{ $inquiry->status === 'resolved' ? 'selected' : '' }}>Resolved</option>
                                    <option value="closed" {{ $inquiry->status === 'closed' ? 'selected' : '' }}>Closed</option>
                                </select>
                            </div>

                            <!-- Priority -->
                            <div>
                                <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                                    Priority <span class="text-red-500">*</span>
                                </label>
                                <select id="priority" 
                                        name="priority" 
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    <option value="low" {{ $inquiry->priority === 'low' ? 'selected' : '' }}>Low</option>
                                    <option value="medium" {{ $inquiry->priority === 'medium' ? 'selected' : '' }}>Medium</option>
                                    <option value="high" {{ $inquiry->priority === 'high' ? 'selected' : '' }}>High</option>
                                    <option value="urgent" {{ $inquiry->priority === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                </select>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Internal Notes
                            </label>
                            <textarea id="notes" 
                                      name="notes" 
                                      rows="4"
                                      placeholder="Add internal notes about this inquiry..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">{{ $inquiry->notes }}</textarea>
                            <p class="mt-1 text-xs text-gray-500">These notes are for internal use only and won't be visible to the customer</p>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-save mr-2"></i>
                                Update Inquiry
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Add Response -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Add Response</h3>
                    
                    <form method="POST" action="{{ route('admin.inquiries.respond', $inquiry) }}" class="space-y-6">
                        @csrf
                        
                        <!-- Response -->
                        <div>
                            <label for="response" class="block text-sm font-medium text-gray-700 mb-2">
                                Response <span class="text-red-500">*</span>
                            </label>
                            <textarea id="response" 
                                      name="response" 
                                      rows="6"
                                      required
                                      placeholder="Write your response to the customer..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"></textarea>
                        </div>

                        <!-- Status after response -->
                        <div>
                            <label for="response_status" class="block text-sm font-medium text-gray-700 mb-2">
                                Status after response <span class="text-red-500">*</span>
                            </label>
                            <select id="response_status" 
                                    name="status" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="in_progress">In Progress</option>
                                <option value="resolved">Resolved</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" 
                                    class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-reply mr-2"></i>
                                Add Response
                            </button>
                        </div>
                    </form>
                </div>
            @endif

            <!-- Responses -->
            @if($inquiry->responses && count($inquiry->responses) > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Responses</h3>
                    
                    <div class="space-y-6">
                        @foreach($inquiry->responses as $response)
                            <div class="border-l-4 border-blue-500 pl-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="font-medium text-gray-900">{{ $response['user_name'] }}</span>
                                    <span class="text-sm text-gray-500">{{ \Carbon\Carbon::parse($response['created_at'])->format('M d, Y H:i') }}</span>
                                </div>
                                <p class="text-gray-700 leading-relaxed whitespace-pre-wrap">{{ $response['response'] }}</p>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Status</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Status</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $inquiry->status === 'new' ? 'bg-yellow-100 text-yellow-800' : 
                               ($inquiry->status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 
                                ($inquiry->status === 'resolved' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')) }}">
                            {{ ucfirst(str_replace('_', ' ', $inquiry->status)) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Priority</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $inquiry->priority === 'urgent' ? 'bg-red-100 text-red-800' : 
                               ($inquiry->priority === 'high' ? 'bg-orange-100 text-orange-800' : 
                                ($inquiry->priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                            {{ ucfirst($inquiry->priority) }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Read Status</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $inquiry->is_read ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800' }}">
                            {{ $inquiry->is_read ? 'Read' : 'Unread' }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Assigned To</span>
                        @if($inquiry->assignedTo)
                            <div class="flex items-center">
                                <img src="{{ $inquiry->assignedTo->avatar_url }}" alt="{{ $inquiry->assignedTo->name }}" class="w-5 h-5 rounded-full mr-1">
                                <span class="text-sm text-gray-900">{{ $inquiry->assignedTo->name }}</span>
                            </div>
                        @else
                            <span class="text-sm text-gray-500">Unassigned</span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Metadata</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Received</label>
                        <p class="text-gray-900">{{ $inquiry->created_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $inquiry->created_at->diffForHumans() }}</p>
                    </div>
                    
                    @if($inquiry->responded_at)
                        <div>
                            <label class="block text-gray-600 mb-1">Last Response</label>
                            <p class="text-gray-900">{{ $inquiry->responded_at->format('M d, Y H:i') }}</p>
                            <p class="text-gray-500">{{ $inquiry->responded_at->diffForHumans() }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Inquiry ID</label>
                        <p class="text-gray-900 font-mono">#{{ $inquiry->id }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    <a href="mailto:{{ $inquiry->email }}?subject=Re: {{ $inquiry->subject }}" 
                       class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                        <i class="fas fa-envelope mr-2"></i>
                        Email Customer
                    </a>
                    
                    @if($inquiry->phone)
                        <a href="tel:{{ $inquiry->phone }}" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                            <i class="fas fa-phone mr-2"></i>
                            Call Customer
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.inquiries.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Inquiries
                    </a>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Message Length</span>
                        <span class="font-medium text-gray-900">{{ strlen($inquiry->message) }} chars</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Word Count</span>
                        <span class="font-medium text-gray-900">{{ str_word_count($inquiry->message) }} words</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Responses</span>
                        <span class="font-medium text-gray-900">{{ $inquiry->responses ? count($inquiry->responses) : 0 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Has Phone</span>
                        <span class="font-medium text-gray-900">{{ $inquiry->phone ? 'Yes' : 'No' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
