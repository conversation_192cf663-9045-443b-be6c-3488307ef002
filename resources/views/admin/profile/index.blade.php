<x-admin-layout title="Profile">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">My Profile</h1>
        <p class="text-gray-600">Manage your account settings and preferences</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                
                <form method="POST" action="{{ route('admin.profile.update') }}">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Full Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $user->name) }}" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Address <span class="text-red-500">*</span>
                            </label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email', $user->email) }}" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-500 @enderror">
                            @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                Phone Number
                            </label>
                            <input type="text" 
                                   id="phone" 
                                   name="phone" 
                                   value="{{ old('phone', $user->phone) }}"
                                   placeholder="+255 XXX XXX XXX"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('phone') border-red-500 @enderror">
                            @error('phone')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Role (Read-only) -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <div class="w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-lg">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium
                                    {{ $user->role->name === 'super_admin' ? 'bg-red-100 text-red-800' : 
                                       ($user->role->name === 'admin' ? 'bg-blue-100 text-blue-800' : 
                                        ($user->role->name === 'editor' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')) }}">
                                    {{ $user->role->display_name }}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button type="submit" 
                                class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>

            <!-- Change Password -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Change Password</h3>
                
                <form method="POST" action="{{ route('admin.profile.password') }}">
                    @csrf
                    
                    <div class="space-y-6">
                        <!-- Current Password -->
                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                                Current Password <span class="text-red-500">*</span>
                            </label>
                            <input type="password" 
                                   id="current_password" 
                                   name="current_password" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('current_password') border-red-500 @enderror">
                            @error('current_password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- New Password -->
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                    New Password <span class="text-red-500">*</span>
                                </label>
                                <input type="password" 
                                       id="password" 
                                       name="password" 
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('password') border-red-500 @enderror">
                                @error('password')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Minimum 8 characters</p>
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                                    Confirm New Password <span class="text-red-500">*</span>
                                </label>
                                <input type="password" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button type="submit" 
                                class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-key mr-2"></i>
                            Update Password
                        </button>
                    </div>
                </form>
            </div>

            <!-- Preferences -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Preferences</h3>
                
                <form method="POST" action="{{ route('admin.profile.preferences') }}">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Timezone -->
                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                                Timezone
                            </label>
                            <select id="timezone" 
                                    name="timezone"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select Timezone</option>
                                <option value="Africa/Dar_es_Salaam" {{ old('timezone', $user->preferences['timezone'] ?? '') === 'Africa/Dar_es_Salaam' ? 'selected' : '' }}>
                                    Africa/Dar es Salaam (EAT)
                                </option>
                                <option value="UTC" {{ old('timezone', $user->preferences['timezone'] ?? '') === 'UTC' ? 'selected' : '' }}>
                                    UTC
                                </option>
                            </select>
                        </div>

                        <!-- Language -->
                        <div>
                            <label for="language" class="block text-sm font-medium text-gray-700 mb-2">
                                Language
                            </label>
                            <select id="language" 
                                    name="language"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <option value="en" {{ old('language', $user->preferences['language'] ?? 'en') === 'en' ? 'selected' : '' }}>
                                    English
                                </option>
                                <option value="sw" {{ old('language', $user->preferences['language'] ?? '') === 'sw' ? 'selected' : '' }}>
                                    Kiswahili
                                </option>
                            </select>
                        </div>
                    </div>

                    <!-- Notification Preferences -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">Notification Preferences</h4>
                        
                        <div class="space-y-3">
                            <!-- Email Notifications -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="email_notifications" 
                                       name="email_notifications" 
                                       value="1" 
                                       {{ old('email_notifications', $user->preferences['email_notifications'] ?? true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="email_notifications" class="ml-2 block text-sm text-gray-900">
                                    Email Notifications
                                </label>
                            </div>

                            <!-- Browser Notifications -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="browser_notifications" 
                                       name="browser_notifications" 
                                       value="1" 
                                       {{ old('browser_notifications', $user->preferences['browser_notifications'] ?? false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="browser_notifications" class="ml-2 block text-sm text-gray-900">
                                    Browser Notifications
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button type="submit" 
                                class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-cog mr-2"></i>
                            Update Preferences
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Profile Avatar -->
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <div class="relative inline-block">
                    <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="w-24 h-24 rounded-full mx-auto mb-4">
                    <button onclick="document.getElementById('avatar_upload').click()" 
                            class="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 hover:bg-blue-700 transition-colors">
                        <i class="fas fa-camera text-sm"></i>
                    </button>
                    <input type="file" id="avatar_upload" accept="image/*" class="hidden">
                </div>
                <h4 class="text-lg font-semibold text-gray-900">{{ $user->name }}</h4>
                <p class="text-sm text-gray-600">{{ $user->role->display_name }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ $user->email }}</p>
            </div>

            <!-- Account Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Member Since</label>
                        <p class="text-gray-900">{{ $user->created_at->format('M d, Y') }}</p>
                        <p class="text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Last Login</label>
                        <p class="text-gray-900">{{ $user->last_login_at ? $user->last_login_at->format('M d, Y H:i') : 'Never' }}</p>
                        <p class="text-gray-500">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'No login recorded' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Account Status</label>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $user->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.profile.activity') }}" 
                       class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                        <i class="fas fa-history mr-2"></i>
                        View Activity Log
                    </a>
                    
                    <a href="{{ route('admin.dashboard') }}" 
                       class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                        <i class="fas fa-tachometer-alt mr-2"></i>
                        Go to Dashboard
                    </a>
                    
                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <button type="submit" 
                                class="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-sign-out-alt mr-2"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>

            <!-- Recent Activity -->
            @if($recentActivities->count() > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h4>
                    
                    <div class="space-y-3">
                        @foreach($recentActivities->take(5) as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-6 h-6 bg-{{ $activity->action === 'created' ? 'green' : ($activity->action === 'updated' ? 'blue' : ($activity->action === 'deleted' ? 'red' : 'gray')) }}-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-{{ $activity->action === 'created' ? 'plus' : ($activity->action === 'updated' ? 'edit' : ($activity->action === 'deleted' ? 'trash' : 'eye')) }} text-{{ $activity->action === 'created' ? 'green' : ($activity->action === 'updated' ? 'blue' : ($activity->action === 'deleted' ? 'red' : 'gray')) }}-600 text-xs"></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-xs text-gray-900">{{ Str::limit($activity->description, 40) }}</p>
                                    <p class="text-xs text-gray-500">{{ $activity->created_at->diffForHumans() }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    @if($recentActivities->count() > 5)
                        <div class="mt-4 text-center">
                            <a href="{{ route('admin.profile.activity') }}" class="text-blue-600 hover:text-blue-700 text-sm">
                                View All Activity
                            </a>
                        </div>
                    @endif
                </div>
            @endif
        </div>
    </div>

    <x-slot name="scripts">
        <script>
            // Avatar upload
            document.getElementById('avatar_upload').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const formData = new FormData();
                    formData.append('avatar', file);
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

                    fetch('{{ route("admin.profile.avatar") }}', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Upload failed');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Upload failed');
                    });
                }
            });
        </script>
    </x-slot>
</x-admin-layout>
