<x-admin-layout title="Create Role">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.roles.index') }}" class="hover:text-blue-600">Roles & Permissions</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Create Role</span>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900">Create New Role</h1>
        <p class="text-gray-600">Define a new role with specific permissions</p>
    </div>

    <form method="POST" action="{{ route('admin.roles.store') }}" class="space-y-8">
        @csrf

        <!-- Basic Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Role Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Role Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name') }}" 
                           required
                           placeholder="e.g., content_manager"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Use lowercase letters, numbers, and underscores only</p>
                </div>

                <!-- Display Name -->
                <div>
                    <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Display Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="display_name" 
                           name="display_name" 
                           value="{{ old('display_name') }}" 
                           required
                           placeholder="e.g., Content Manager"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('display_name') border-red-500 @enderror">
                    @error('display_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    <p class="mt-1 text-xs text-gray-500">Human-readable name for the role</p>
                </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          placeholder="Describe the purpose and responsibilities of this role..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Permissions -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900">Permissions</h3>
                <div class="flex items-center space-x-4">
                    <button type="button" onclick="selectAllPermissions()" class="text-sm text-blue-600 hover:text-blue-700">
                        Select All
                    </button>
                    <button type="button" onclick="deselectAllPermissions()" class="text-sm text-gray-600 hover:text-gray-700">
                        Deselect All
                    </button>
                </div>
            </div>

            @if($permissionGroups->count() > 0)
                <div class="space-y-6">
                    @foreach($permissionGroups as $group => $groupPermissions)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-md font-semibold text-gray-900">
                                    {{ ucfirst(str_replace('_', ' ', $group)) }} Permissions
                                </h4>
                                <div class="flex items-center space-x-2">
                                    <button type="button" onclick="selectGroupPermissions('{{ $group }}')" class="text-xs text-blue-600 hover:text-blue-700">
                                        Select All
                                    </button>
                                    <button type="button" onclick="deselectGroupPermissions('{{ $group }}')" class="text-xs text-gray-600 hover:text-gray-700">
                                        Deselect All
                                    </button>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                @foreach($groupPermissions as $permission)
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                               id="permission_{{ $permission->id }}" 
                                               name="permissions[]" 
                                               value="{{ $permission->id }}"
                                               data-group="{{ $group }}"
                                               {{ in_array($permission->id, old('permissions', [])) ? 'checked' : '' }}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="permission_{{ $permission->id }}" class="ml-2 block text-sm text-gray-900">
                                            {{ $permission->display_name }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">No permissions available</p>
                </div>
            @endif

            @error('permissions')
                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between">
            <a href="{{ route('admin.roles.index') }}" 
               class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Cancel
            </a>
            
            <button type="submit" 
                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-save mr-2"></i>
                Create Role
            </button>
        </div>
    </form>

    <x-slot name="scripts">
        <script>
            function selectAllPermissions() {
                document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
                    checkbox.checked = true;
                });
            }

            function deselectAllPermissions() {
                document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
            }

            function selectGroupPermissions(group) {
                document.querySelectorAll(`input[data-group="${group}"]`).forEach(checkbox => {
                    checkbox.checked = true;
                });
            }

            function deselectGroupPermissions(group) {
                document.querySelectorAll(`input[data-group="${group}"]`).forEach(checkbox => {
                    checkbox.checked = false;
                });
            }

            // Auto-generate display name from role name
            document.getElementById('name').addEventListener('input', function() {
                const name = this.value;
                const displayName = name
                    .replace(/_/g, ' ')
                    .replace(/\b\w/g, l => l.toUpperCase());
                
                if (!document.getElementById('display_name').value) {
                    document.getElementById('display_name').value = displayName;
                }
            });
        </script>
    </x-slot>
</x-admin-layout>
