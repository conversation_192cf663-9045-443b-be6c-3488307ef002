<x-admin-layout title="Edit Role">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.roles.index') }}" class="hover:text-blue-600">Roles & Permissions</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Edit Role</span>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900">Edit Role: {{ $role->display_name }}</h1>
        <p class="text-gray-600">Update role information and permissions</p>
    </div>

    <form method="POST" action="{{ route('admin.roles.update', $role) }}" class="space-y-8">
        @csrf
        @method('PUT')

        <!-- Basic Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Role Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Role Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $role->name) }}" 
                           required
                           {{ in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']) ? 'readonly' : '' }}
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror {{ in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']) ? 'bg-gray-100' : '' }}">
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                    @if(in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']))
                        <p class="mt-1 text-xs text-yellow-600">System role names cannot be changed</p>
                    @else
                        <p class="mt-1 text-xs text-gray-500">Use lowercase letters, numbers, and underscores only</p>
                    @endif
                </div>

                <!-- Display Name -->
                <div>
                    <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Display Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="display_name" 
                           name="display_name" 
                           value="{{ old('display_name', $role->display_name) }}" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('display_name') border-red-500 @enderror">
                    @error('display_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div class="mt-6">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $role->description) }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Permissions -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-medium text-gray-900">Permissions</h3>
                <div class="flex items-center space-x-4">
                    <button type="button" onclick="selectAllPermissions()" class="text-sm text-blue-600 hover:text-blue-700">
                        Select All
                    </button>
                    <button type="button" onclick="deselectAllPermissions()" class="text-sm text-gray-600 hover:text-gray-700">
                        Deselect All
                    </button>
                </div>
            </div>

            @if($permissionGroups->count() > 0)
                <div class="space-y-6">
                    @foreach($permissionGroups as $group => $groupPermissions)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-md font-semibold text-gray-900">
                                    {{ ucfirst(str_replace('_', ' ', $group)) }} Permissions
                                </h4>
                                <div class="flex items-center space-x-2">
                                    <button type="button" onclick="selectGroupPermissions('{{ $group }}')" class="text-xs text-blue-600 hover:text-blue-700">
                                        Select All
                                    </button>
                                    <button type="button" onclick="deselectGroupPermissions('{{ $group }}')" class="text-xs text-gray-600 hover:text-gray-700">
                                        Deselect All
                                    </button>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                @foreach($groupPermissions as $permission)
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                               id="permission_{{ $permission->id }}" 
                                               name="permissions[]" 
                                               value="{{ $permission->id }}"
                                               data-group="{{ $group }}"
                                               {{ in_array($permission->id, old('permissions', $rolePermissions)) ? 'checked' : '' }}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="permission_{{ $permission->id }}" class="ml-2 block text-sm text-gray-900">
                                            {{ $permission->display_name }}
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">No permissions available</p>
                </div>
            @endif

            @error('permissions')
                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
            @enderror
        </div>

        <!-- Role Information -->
        <div class="bg-gray-50 rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Role Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div>
                    <label class="block text-gray-600 mb-1">Created</label>
                    <p class="text-gray-900">{{ $role->created_at->format('M d, Y') }}</p>
                    <p class="text-gray-500">{{ $role->created_at->diffForHumans() }}</p>
                </div>
                
                <div>
                    <label class="block text-gray-600 mb-1">Last Updated</label>
                    <p class="text-gray-900">{{ $role->updated_at->format('M d, Y') }}</p>
                    <p class="text-gray-500">{{ $role->updated_at->diffForHumans() }}</p>
                </div>
                
                <div>
                    <label class="block text-gray-600 mb-1">Assigned Users</label>
                    <p class="text-gray-900">{{ $role->users()->count() }} {{ Str::plural('user', $role->users()->count()) }}</p>
                    @if($role->users()->count() > 0)
                        <p class="text-gray-500">Changes will affect all assigned users</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.roles.index') }}" 
                   class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Cancel
                </a>
                
                @if(auth()->user()->hasPermission('view_roles'))
                    <a href="{{ route('admin.roles.show', $role) }}" 
                       class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        View Role
                    </a>
                @endif
            </div>
            
            <button type="submit" 
                    class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-save mr-2"></i>
                Update Role
            </button>
        </div>
    </form>

    <x-slot name="scripts">
        <script>
            function selectAllPermissions() {
                document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
                    checkbox.checked = true;
                });
            }

            function deselectAllPermissions() {
                document.querySelectorAll('input[name="permissions[]"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
            }

            function selectGroupPermissions(group) {
                document.querySelectorAll(`input[data-group="${group}"]`).forEach(checkbox => {
                    checkbox.checked = true;
                });
            }

            function deselectGroupPermissions(group) {
                document.querySelectorAll(`input[data-group="${group}"]`).forEach(checkbox => {
                    checkbox.checked = false;
                });
            }
        </script>
    </x-slot>
</x-admin-layout>
