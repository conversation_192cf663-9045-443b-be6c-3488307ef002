<x-admin-layout title="Roles & Permissions">
    <div class="mb-6 flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Roles & Permissions</h1>
            <p class="text-gray-600">Manage user roles and their permissions</p>
        </div>
        
        @if(auth()->user()->hasPermission('create_roles'))
            <a href="{{ route('admin.roles.create') }}" 
               class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Add New Role
            </a>
        @endif
    </div>

    <!-- Roles Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($roles as $role)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- Role Header -->
                <div class="bg-gradient-to-r {{ $role->name === 'super_admin' ? 'from-red-500 to-red-600' : ($role->name === 'admin' ? 'from-blue-500 to-blue-600' : ($role->name === 'editor' ? 'from-green-500 to-green-600' : 'from-gray-500 to-gray-600')) }} p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold">{{ $role->display_name }}</h3>
                            <p class="text-sm opacity-90">{{ $role->name }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold">{{ $role->users_count }}</div>
                            <div class="text-xs opacity-90">{{ Str::plural('User', $role->users_count) }}</div>
                        </div>
                    </div>
                </div>

                <!-- Role Content -->
                <div class="p-6">
                    <!-- Description -->
                    <div class="mb-4">
                        <p class="text-gray-600 text-sm">
                            {{ $role->description ?: 'No description provided.' }}
                        </p>
                    </div>

                    <!-- Permissions Count -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Permissions</span>
                            <span class="font-semibold text-gray-900">{{ $role->permissions_count }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(100, ($role->permissions_count / 38) * 100) }}%"></div>
                        </div>
                    </div>

                    <!-- System Role Badge -->
                    @if(in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']))
                        <div class="mb-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-shield-alt mr-1"></i>
                                System Role
                            </span>
                        </div>
                    @endif

                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div class="flex items-center space-x-2">
                            @if(auth()->user()->hasPermission('view_roles'))
                                <a href="{{ route('admin.roles.show', $role) }}" 
                                   class="text-blue-600 hover:text-blue-900 text-sm">
                                    <i class="fas fa-eye mr-1"></i>
                                    View
                                </a>
                            @endif
                            
                            @if(auth()->user()->hasPermission('edit_roles') && ($role->name !== 'super_admin' || auth()->user()->role->name === 'super_admin'))
                                <a href="{{ route('admin.roles.edit', $role) }}" 
                                   class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    <i class="fas fa-edit mr-1"></i>
                                    Edit
                                </a>
                            @endif
                        </div>
                        
                        @if(auth()->user()->hasPermission('delete_roles') && !in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']) && $role->users_count === 0)
                            <form method="POST" action="{{ route('admin.roles.destroy', $role) }}" 
                                  class="inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this role?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="text-red-600 hover:text-red-900 text-sm">
                                    <i class="fas fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Empty State -->
    @if($roles->count() === 0)
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <i class="fas fa-user-shield text-4xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No roles found</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first role.</p>
            @if(auth()->user()->hasPermission('create_roles'))
                <a href="{{ route('admin.roles.create') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Add New Role
                </a>
            @endif
        </div>
    @endif

    <!-- Statistics -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-user-shield text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $roles->count() }}</h3>
                    <p class="text-sm text-gray-600">Total Roles</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $roles->sum('users_count') }}</h3>
                    <p class="text-sm text-gray-600">Total Users</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-shield-alt text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">{{ $roles->whereIn('name', ['super_admin', 'admin', 'editor', 'viewer'])->count() }}</h3>
                    <p class="text-sm text-gray-600">System Roles</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-key text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-900">38</h3>
                    <p class="text-sm text-gray-600">Total Permissions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Information -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-900 mb-4">Role Management Guidelines</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
                <h4 class="font-semibold text-blue-800 mb-2">System Roles</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• <strong>Super Admin:</strong> Full system access</li>
                    <li>• <strong>Admin:</strong> Content and user management</li>
                    <li>• <strong>Editor:</strong> Content creation and editing</li>
                    <li>• <strong>Viewer:</strong> Read-only access</li>
                </ul>
            </div>
            <div>
                <h4 class="font-semibold text-blue-800 mb-2">Best Practices</h4>
                <ul class="space-y-1 text-blue-700">
                    <li>• Assign minimum required permissions</li>
                    <li>• Regularly review role assignments</li>
                    <li>• Use descriptive role names</li>
                    <li>• Document role purposes</li>
                </ul>
            </div>
        </div>
    </div>
</x-admin-layout>
