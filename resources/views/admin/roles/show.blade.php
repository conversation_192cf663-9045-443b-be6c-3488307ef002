<x-admin-layout title="Role Details">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.roles.index') }}" class="hover:text-blue-600">Roles & Permissions</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ $role->display_name }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $role->display_name }}</h1>
                <p class="text-gray-600">{{ $role->description ?: 'No description provided.' }}</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_roles') && ($role->name !== 'super_admin' || auth()->user()->role->name === 'super_admin'))
                    <a href="{{ route('admin.roles.edit', $role) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Role
                    </a>
                @endif
                
                @if(in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']))
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                        <i class="fas fa-shield-alt mr-1"></i>
                        System Role
                    </span>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Role Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Role Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Role Name</label>
                        <p class="text-gray-900 font-mono text-sm bg-gray-100 px-2 py-1 rounded">{{ $role->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Display Name</label>
                        <p class="text-gray-900">{{ $role->display_name }}</p>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-600 mb-1">Description</label>
                        <p class="text-gray-900">{{ $role->description ?: 'No description provided.' }}</p>
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Permissions</h3>
                    <span class="text-sm text-gray-600">{{ $role->permissions->count() }} of 38 permissions</span>
                </div>
                
                @if($permissionGroups->count() > 0)
                    <div class="space-y-6">
                        @foreach($permissionGroups as $group => $groupPermissions)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h4 class="text-md font-semibold text-gray-900 mb-3">
                                    {{ ucfirst(str_replace('_', ' ', $group)) }} Permissions
                                    <span class="text-sm font-normal text-gray-600">({{ $groupPermissions->count() }})</span>
                                </h4>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    @foreach($groupPermissions as $permission)
                                        <div class="flex items-center">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            <span class="text-sm text-gray-900">{{ $permission->display_name }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-key text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No permissions assigned to this role</p>
                    </div>
                @endif
            </div>

            <!-- Assigned Users -->
            @if($role->users->count() > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        Assigned Users ({{ $role->users->count() }})
                    </h3>
                    
                    <div class="space-y-3">
                        @foreach($role->users as $user)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="w-8 h-8 rounded-full mr-3">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $user->name }}</p>
                                        <p class="text-xs text-gray-500">{{ $user->email }}</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $user->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                    
                                    @if(auth()->user()->hasPermission('view_users'))
                                        <a href="{{ route('admin.users.show', $user) }}" 
                                           class="text-blue-600 hover:text-blue-900 text-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Role Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total Permissions</span>
                        <span class="text-lg font-semibold text-gray-900">{{ $role->permissions->count() }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Assigned Users</span>
                        <span class="text-lg font-semibold text-gray-900">{{ $role->users->count() }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Permission Coverage</span>
                        <span class="text-lg font-semibold text-gray-900">{{ round(($role->permissions->count() / 38) * 100) }}%</span>
                    </div>
                </div>
                
                <!-- Permission Coverage Bar -->
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min(100, ($role->permissions->count() / 38) * 100) }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Role Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Role Details</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Created</label>
                        <p class="text-gray-900">{{ $role->created_at->format('M d, Y') }}</p>
                        <p class="text-gray-500">{{ $role->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $role->updated_at->format('M d, Y') }}</p>
                        <p class="text-gray-500">{{ $role->updated_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Role Type</label>
                        <p class="text-gray-900">
                            {{ in_array($role->name, ['super_admin', 'admin', 'editor', 'viewer']) ? 'System Role' : 'Custom Role' }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_roles') && ($role->name !== 'super_admin' || auth()->user()->role->name === 'super_admin'))
                        <a href="{{ route('admin.roles.edit', $role) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Role
                        </a>
                    @endif
                    
                    @if(auth()->user()->hasPermission('view_users'))
                        <a href="{{ route('admin.users.index') }}?role={{ $role->id }}" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                            <i class="fas fa-users mr-2"></i>
                            View Users
                        </a>
                    @endif
                    
                    @if(auth()->user()->hasPermission('create_roles'))
                        <a href="{{ route('admin.roles.create') }}" 
                           class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            Create New Role
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
