<x-admin-layout title="Edit Service Category">
    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <a href="{{ route('admin.service-categories.index') }}" 
               class="text-gray-600 hover:text-gray-900">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Service Category</h1>
                <p class="text-gray-600">Update the service category information</p>
            </div>
        </div>
    </div>

    <div class="max-w-2xl">
        <form method="POST" action="{{ route('admin.service-categories.update', $serviceCategory) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Basic Information</h3>
                
                <div class="grid grid-cols-1 gap-6">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Category Name *
                        </label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="{{ old('name', $serviceCategory->name) }}"
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., Work Abroad">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            URL Slug
                        </label>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               value="{{ old('slug', $serviceCategory->slug) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Auto-generated from name">
                        <p class="mt-1 text-xs text-gray-500">Leave empty to auto-generate from name</p>
                        @error('slug')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Brief description of this service category">{{ old('description', $serviceCategory->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Icon -->
                    <div>
                        <label for="icon" class="block text-sm font-medium text-gray-700 mb-2">
                            Icon Class
                        </label>
                        <input type="text" 
                               id="icon" 
                               name="icon" 
                               value="{{ old('icon', $serviceCategory->icon) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                               placeholder="e.g., fas fa-briefcase">
                        <p class="mt-1 text-xs text-gray-500">FontAwesome icon class</p>
                        @error('icon')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Appearance -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Appearance</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Color From -->
                    <div>
                        <label for="color_from" class="block text-sm font-medium text-gray-700 mb-2">
                            Gradient Start Color *
                        </label>
                        <select id="color_from" 
                                name="color_from" 
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            <option value="blue-500" {{ old('color_from', $serviceCategory->color_from) == 'blue-500' ? 'selected' : '' }}>Blue</option>
                            <option value="green-500" {{ old('color_from', $serviceCategory->color_from) == 'green-500' ? 'selected' : '' }}>Green</option>
                            <option value="purple-500" {{ old('color_from', $serviceCategory->color_from) == 'purple-500' ? 'selected' : '' }}>Purple</option>
                            <option value="indigo-500" {{ old('color_from', $serviceCategory->color_from) == 'indigo-500' ? 'selected' : '' }}>Indigo</option>
                            <option value="red-500" {{ old('color_from', $serviceCategory->color_from) == 'red-500' ? 'selected' : '' }}>Red</option>
                            <option value="yellow-500" {{ old('color_from', $serviceCategory->color_from) == 'yellow-500' ? 'selected' : '' }}>Yellow</option>
                        </select>
                        @error('color_from')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Color To -->
                    <div>
                        <label for="color_to" class="block text-sm font-medium text-gray-700 mb-2">
                            Gradient End Color *
                        </label>
                        <select id="color_to" 
                                name="color_to" 
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            <option value="indigo-600" {{ old('color_to', $serviceCategory->color_to) == 'indigo-600' ? 'selected' : '' }}>Indigo</option>
                            <option value="teal-600" {{ old('color_to', $serviceCategory->color_to) == 'teal-600' ? 'selected' : '' }}>Teal</option>
                            <option value="pink-600" {{ old('color_to', $serviceCategory->color_to) == 'pink-600' ? 'selected' : '' }}>Pink</option>
                            <option value="purple-600" {{ old('color_to', $serviceCategory->color_to) == 'purple-600' ? 'selected' : '' }}>Purple</option>
                            <option value="red-600" {{ old('color_to', $serviceCategory->color_to) == 'red-600' ? 'selected' : '' }}>Red</option>
                            <option value="orange-600" {{ old('color_to', $serviceCategory->color_to) == 'orange-600' ? 'selected' : '' }}>Orange</option>
                        </select>
                        @error('color_to')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Settings</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            Sort Order
                        </label>
                        <input type="number" 
                               id="sort_order" 
                               name="sort_order" 
                               value="{{ old('sort_order', $serviceCategory->sort_order) }}"
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_active" 
                                   name="is_active" 
                                   value="1" 
                                   {{ old('is_active', $serviceCategory->is_active) ? 'checked' : '' }}
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('admin.service-categories.index') }}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Update Category
                </button>
            </div>
        </form>
    </div>
</x-admin-layout>
