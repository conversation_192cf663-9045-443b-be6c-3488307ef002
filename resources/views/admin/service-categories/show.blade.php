<x-admin-layout title="Service Category Details">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.service-categories.index') }}" 
                   class="text-gray-600 hover:text-gray-900">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">{{ $serviceCategory->name }}</h1>
                    <p class="text-gray-600">Service category details and management</p>
                </div>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_services'))
                    <a href="{{ route('admin.service-categories.edit', $serviceCategory) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Category
                    </a>
                @endif
                
                @if(auth()->user()->hasPermission('delete_services') && $serviceCategory->services->count() == 0)
                    <form method="POST" action="{{ route('admin.service-categories.destroy', $serviceCategory) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this category?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Category Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center mb-6">
                    @if($serviceCategory->icon)
                        <div class="h-16 w-16 rounded-lg bg-gradient-to-r {{ $serviceCategory->gradient_class }} flex items-center justify-center mr-4">
                            <i class="{{ $serviceCategory->icon }} text-2xl text-white"></i>
                        </div>
                    @endif
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">{{ $serviceCategory->name }}</h2>
                        <p class="text-gray-600">{{ $serviceCategory->slug }}</p>
                    </div>
                </div>

                @if($serviceCategory->description)
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                    <p class="text-gray-600">{{ $serviceCategory->description }}</p>
                </div>
                @endif

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Status</h4>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1
                            {{ $serviceCategory->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $serviceCategory->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Sort Order</h4>
                        <p class="text-sm text-gray-900 mt-1">{{ $serviceCategory->sort_order }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Created</h4>
                        <p class="text-sm text-gray-900 mt-1">{{ $serviceCategory->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500">Last Updated</h4>
                        <p class="text-sm text-gray-900 mt-1">{{ $serviceCategory->updated_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>

            <!-- Services in this Category -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Services in this Category ({{ $serviceCategory->services->count() }})
                    </h3>
                    @if(auth()->user()->hasPermission('create_services'))
                        <a href="{{ route('admin.services.create') }}?category={{ $serviceCategory->id }}" 
                           class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                            <i class="fas fa-plus mr-1"></i>
                            Add Service
                        </a>
                    @endif
                </div>

                @if($serviceCategory->services->count() > 0)
                    <div class="space-y-4">
                        @foreach($serviceCategory->services as $service)
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gradient-to-br {{ $service->gradient_class }} rounded-lg flex items-center justify-center mr-3">
                                            @if($service->icon)
                                                <i class="{{ $service->icon }} text-white"></i>
                                            @else
                                                <i class="fas fa-concierge-bell text-white"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-900">{{ $service->title }}</h4>
                                            <p class="text-sm text-gray-600">{{ Str::limit($service->description, 60) }}</p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $service->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $service->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                        @if($service->is_featured)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Featured
                                            </span>
                                        @endif
                                        @if(auth()->user()->hasPermission('view_services'))
                                            <a href="{{ route('admin.services.show', $service) }}" 
                                               class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        @endif
                                        @if(auth()->user()->hasPermission('edit_services'))
                                            <a href="{{ route('admin.services.edit', $service) }}" 
                                               class="text-indigo-600 hover:text-indigo-900">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No services in this category yet.</p>
                        @if(auth()->user()->hasPermission('create_services'))
                            <a href="{{ route('admin.services.create') }}?category={{ $serviceCategory->id }}" 
                               class="mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                                <i class="fas fa-plus mr-2"></i>
                                Add First Service
                            </a>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h4>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Total Services</span>
                        <span class="font-semibold text-gray-900">{{ $serviceCategory->services->count() }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Active Services</span>
                        <span class="font-semibold text-gray-900">{{ $serviceCategory->services->where('is_active', true)->count() }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Featured Services</span>
                        <span class="font-semibold text-gray-900">{{ $serviceCategory->services->where('is_featured', true)->count() }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_services'))
                        <a href="{{ route('admin.service-categories.edit', $serviceCategory) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Category
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.service-categories.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Categories
                    </a>
                    
                    @if(auth()->user()->hasPermission('create_services'))
                        <a href="{{ route('admin.services.create') }}?category={{ $serviceCategory->id }}" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            Add Service
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
