<x-admin-layout title="Edit Service">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.services.index') }}" class="hover:text-blue-600">Services</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Edit Service</span>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900">Edit Service</h1>
        <p class="text-gray-600">Update service information and settings</p>
    </div>

    <div class="bg-white rounded-lg shadow">
        <form method="POST" action="{{ route('admin.services.update', $service) }}" class="p-6 space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Service Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               value="{{ old('title', $service->title) }}" 
                               required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('title') border-red-500 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Slug -->
                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            URL Slug
                        </label>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               value="{{ old('slug', $service->slug) }}"
                               placeholder="auto-generated-from-title"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('slug') border-red-500 @enderror">
                        @error('slug')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">Leave empty to auto-generate from title</p>
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="service_category_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Category <span class="text-red-500">*</span>
                        </label>
                        <select id="service_category_id"
                                name="service_category_id"
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('service_category_id') border-red-500 @enderror">
                            <option value="">Select Category</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ old('service_category_id', $service->service_category_id) == $category->id ? 'selected' : '' }}>
                                    {{ $category->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('service_category_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                            Sort Order
                        </label>
                        <input type="number" 
                               id="sort_order" 
                               name="sort_order" 
                               value="{{ old('sort_order', $service->sort_order) }}"
                               min="0"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('sort_order') border-red-500 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description <span class="text-red-500">*</span>
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror">{{ old('description', $service->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Features -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Service Features</h3>
                
                <div id="features-container">
                    @php
                        $features = old('features', $service->features ?? []);
                    @endphp
                    
                    @if(count($features) > 0)
                        @foreach($features as $feature)
                            <div class="feature-item flex items-center space-x-2 mb-3">
                                <input type="text" 
                                       name="features[]" 
                                       value="{{ $feature }}"
                                       placeholder="Enter a service feature"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        @endforeach
                    @else
                        <div class="feature-item flex items-center space-x-2 mb-3">
                            <input type="text" 
                                   name="features[]" 
                                   placeholder="Enter a service feature"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                            <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    @endif
                </div>
                
                <button type="button" onclick="addFeature()" class="text-blue-600 hover:text-blue-800 text-sm">
                    <i class="fas fa-plus mr-1"></i>
                    Add Feature
                </button>
            </div>

            <!-- Styling -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Visual Styling</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Color From -->
                    <div>
                        <label for="color_from" class="block text-sm font-medium text-gray-700 mb-2">
                            Gradient Start Color <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="color_from" 
                               name="color_from" 
                               value="{{ old('color_from', $service->color_from) }}" 
                               required
                               placeholder="from-blue-500"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('color_from') border-red-500 @enderror">
                        @error('color_from')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Color To -->
                    <div>
                        <label for="color_to" class="block text-sm font-medium text-gray-700 mb-2">
                            Gradient End Color <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="color_to" 
                               name="color_to" 
                               value="{{ old('color_to', $service->color_to) }}" 
                               required
                               placeholder="to-blue-600"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('color_to') border-red-500 @enderror">
                        @error('color_to')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Status -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Status</h3>
                
                <div class="space-y-3">
                    <!-- Active -->
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               value="1" 
                               {{ old('is_active', $service->is_active) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                            Active Service
                        </label>
                    </div>

                    <!-- Featured -->
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_featured" 
                               name="is_featured" 
                               value="1" 
                               {{ old('is_featured', $service->is_featured) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                            Featured Service
                        </label>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="border-t border-gray-200 pt-6 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.services.index') }}" 
                       class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Services
                    </a>
                    
                    <a href="{{ route('admin.services.show', $service) }}" 
                       class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        View Service
                    </a>
                </div>
                
                <button type="submit" 
                        class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Update Service
                </button>
            </div>
        </form>
    </div>

    <x-slot name="scripts">
        <script>
            function addFeature() {
                const container = document.getElementById('features-container');
                const featureItem = document.createElement('div');
                featureItem.className = 'feature-item flex items-center space-x-2 mb-3';
                featureItem.innerHTML = `
                    <input type="text" 
                           name="features[]" 
                           placeholder="Enter a service feature"
                           class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                    <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(featureItem);
            }

            function removeFeature(button) {
                const container = document.getElementById('features-container');
                if (container.children.length > 1) {
                    button.closest('.feature-item').remove();
                }
            }

            // Auto-generate slug from title if slug is empty
            document.getElementById('title').addEventListener('input', function() {
                const title = this.value;
                const slugField = document.getElementById('slug');
                
                if (!slugField.value || slugField.value === slugField.getAttribute('data-original')) {
                    const slug = title.toLowerCase()
                        .replace(/[^a-z0-9 -]/g, '')
                        .replace(/\s+/g, '-')
                        .replace(/-+/g, '-')
                        .trim('-');
                    
                    slugField.value = slug;
                }
            });

            // Store original slug value
            document.addEventListener('DOMContentLoaded', function() {
                const slugField = document.getElementById('slug');
                slugField.setAttribute('data-original', slugField.value);
            });
        </script>
    </x-slot>
</x-admin-layout>
