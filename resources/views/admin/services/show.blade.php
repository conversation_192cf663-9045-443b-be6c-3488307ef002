<x-admin-layout title="View Service">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.services.index') }}" class="hover:text-blue-600">Services</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ $service->title }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $service->title }}</h1>
                <p class="text-gray-600">Service details and information</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_services'))
                    <a href="{{ route('admin.services.edit', $service) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Service
                    </a>
                @endif
                
                @if(auth()->user()->hasPermission('delete_services'))
                    <form method="POST" action="{{ route('admin.services.destroy', $service) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this service?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                        <p class="text-gray-900">{{ $service->title }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Slug</label>
                        <p class="text-gray-900 font-mono text-sm">{{ $service->slug }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $service->category === 'travel' ? 'bg-blue-100 text-blue-800' : 
                               ($service->category === 'study_abroad' ? 'bg-green-100 text-green-800' : 
                                ($service->category === 'visa' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800')) }}">
                            {{ ucfirst(str_replace('_', ' ', $service->category)) }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
                        <p class="text-gray-900">{{ $service->sort_order }}</p>
                    </div>
                </div>
                
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <p class="text-gray-900 leading-relaxed">{{ $service->description }}</p>
                </div>
            </div>

            <!-- Features -->
            @if($service->features && count($service->features) > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">Service Features</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        @foreach($service->features as $feature)
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-check text-green-600 text-sm"></i>
                                <span class="text-gray-900">{{ $feature }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Visual Preview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Visual Preview</h3>
                
                <div class="bg-gradient-to-r {{ $service->color_from }} {{ $service->color_to }} rounded-lg p-6 text-white">
                    <h4 class="text-xl font-bold mb-2">{{ $service->title }}</h4>
                    <p class="text-white/90 mb-4">{{ Str::limit($service->description, 100) }}</p>
                    
                    @if($service->features && count($service->features) > 0)
                        <div class="space-y-1">
                            @foreach(array_slice($service->features, 0, 3) as $feature)
                                <div class="flex items-center space-x-2 text-sm">
                                    <i class="fas fa-check text-white/80"></i>
                                    <span>{{ $feature }}</span>
                                </div>
                            @endforeach
                            
                            @if(count($service->features) > 3)
                                <p class="text-white/80 text-sm mt-2">
                                    +{{ count($service->features) - 3 }} more features
                                </p>
                            @endif
                        </div>
                    @endif
                </div>
                
                <div class="mt-4 text-sm text-gray-600">
                    <p><strong>Gradient:</strong> {{ $service->color_from }} {{ $service->color_to }}</p>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Status</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Active</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $service->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $service->is_active ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Featured</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $service->is_featured ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $service->is_featured ? 'Yes' : 'No' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Metadata</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Created</label>
                        <p class="text-gray-900">{{ $service->created_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $service->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $service->updated_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $service->updated_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Service ID</label>
                        <p class="text-gray-900 font-mono">#{{ $service->id }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_services'))
                        <a href="{{ route('admin.services.edit', $service) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Service
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.services.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Services
                    </a>
                    
                    @if(auth()->user()->hasPermission('create_services'))
                        <a href="{{ route('admin.services.create') }}" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            New Service
                        </a>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Features Count</span>
                        <span class="font-medium text-gray-900">{{ $service->features ? count($service->features) : 0 }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Description Length</span>
                        <span class="font-medium text-gray-900">{{ strlen($service->description) }} chars</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Category Services</span>
                        <span class="font-medium text-gray-900">
                            {{ \App\Models\Service::where('category', $service->category)->count() }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
