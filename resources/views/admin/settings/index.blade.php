<x-admin-layout title="Settings">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Website Settings</h1>
        <p class="text-gray-600">Manage your website configuration and preferences</p>
    </div>

    <form method="POST" action="{{ route('admin.settings.update') }}" class="space-y-8">
        @csrf

        <!-- Site Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Site Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Site Name -->
                <div>
                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Site Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="site_name" 
                           name="site_name" 
                           value="{{ old('site_name', $settings['site_name'] ?? 'Global Ventures Tanzania') }}" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Site Tagline -->
                <div>
                    <label for="site_tagline" class="block text-sm font-medium text-gray-700 mb-2">
                        Site Tagline
                    </label>
                    <input type="text" 
                           id="site_tagline" 
                           name="site_tagline" 
                           value="{{ old('site_tagline', $settings['site_tagline'] ?? 'Your Gateway to Global Opportunities') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Site URL -->
                <div>
                    <label for="site_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Site URL <span class="text-red-500">*</span>
                    </label>
                    <input type="url" 
                           id="site_url" 
                           name="site_url" 
                           value="{{ old('site_url', $settings['site_url'] ?? config('app.url')) }}" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Logo Upload -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Site Logo</label>
                    <div class="flex items-center space-x-4">
                        @if(isset($settings['site_logo']))
                            <img src="{{ Storage::url($settings['site_logo']) }}" alt="Site Logo" class="w-16 h-16 object-contain border rounded">
                        @else
                            <div class="w-16 h-16 bg-gray-200 border rounded flex items-center justify-center">
                                <i class="fas fa-image text-gray-400"></i>
                            </div>
                        @endif
                        <div>
                            <input type="file" id="logo_upload" accept="image/*" class="hidden">
                            <button type="button" onclick="document.getElementById('logo_upload').click()" 
                                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                Upload Logo
                            </button>
                            <p class="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 2MB</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Site Description -->
            <div class="mt-6">
                <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                    Site Description
                </label>
                <textarea id="site_description" 
                          name="site_description" 
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">{{ old('site_description', $settings['site_description'] ?? '') }}</textarea>
            </div>
        </div>

        <!-- Company Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Company Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Company Name -->
                <div>
                    <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Company Name <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="company_name" 
                           name="company_name" 
                           value="{{ old('company_name', $settings['company_name'] ?? 'Global Ventures Tanzania') }}" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Company Address -->
                <div>
                    <label for="company_address" class="block text-sm font-medium text-gray-700 mb-2">
                        Address
                    </label>
                    <input type="text" 
                           id="company_address" 
                           name="company_address" 
                           value="{{ old('company_address', $settings['company_address'] ?? 'Victoria House Building, New Bagamoyo Road, 8th Floor, Wing B, Office 04') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- City -->
                <div>
                    <label for="company_city" class="block text-sm font-medium text-gray-700 mb-2">
                        City
                    </label>
                    <input type="text" 
                           id="company_city" 
                           name="company_city" 
                           value="{{ old('company_city', $settings['company_city'] ?? 'Dar es Salaam') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Country -->
                <div>
                    <label for="company_country" class="block text-sm font-medium text-gray-700 mb-2">
                        Country
                    </label>
                    <input type="text" 
                           id="company_country" 
                           name="company_country" 
                           value="{{ old('company_country', $settings['company_country'] ?? 'Tanzania') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Contact Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Contact Email -->
                <div>
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Email <span class="text-red-500">*</span>
                    </label>
                    <input type="email" 
                           id="contact_email" 
                           name="contact_email" 
                           value="{{ old('contact_email', $settings['contact_email'] ?? '<EMAIL>') }}" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Contact Phone -->
                <div>
                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Phone
                    </label>
                    <input type="text" 
                           id="contact_phone" 
                           name="contact_phone" 
                           value="{{ old('contact_phone', $settings['contact_phone'] ?? '+255 XXX XXX XXX') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- WhatsApp -->
                <div>
                    <label for="contact_whatsapp" class="block text-sm font-medium text-gray-700 mb-2">
                        WhatsApp Number
                    </label>
                    <input type="text" 
                           id="contact_whatsapp" 
                           name="contact_whatsapp" 
                           value="{{ old('contact_whatsapp', $settings['contact_whatsapp'] ?? '') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Contact Address -->
                <div>
                    <label for="contact_address" class="block text-sm font-medium text-gray-700 mb-2">
                        Contact Address
                    </label>
                    <input type="text" 
                           id="contact_address" 
                           name="contact_address" 
                           value="{{ old('contact_address', $settings['contact_address'] ?? '') }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Social Media -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Social Media Links</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Facebook -->
                <div>
                    <label for="social_facebook" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-facebook text-blue-600 mr-2"></i>Facebook
                    </label>
                    <input type="url" 
                           id="social_facebook" 
                           name="social_facebook" 
                           value="{{ old('social_facebook', $settings['social_facebook'] ?? '') }}"
                           placeholder="https://facebook.com/yourpage"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Instagram -->
                <div>
                    <label for="social_instagram" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-instagram text-pink-600 mr-2"></i>Instagram
                    </label>
                    <input type="url" 
                           id="social_instagram" 
                           name="social_instagram" 
                           value="{{ old('social_instagram', $settings['social_instagram'] ?? '') }}"
                           placeholder="https://instagram.com/yourpage"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- LinkedIn -->
                <div>
                    <label for="social_linkedin" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-linkedin text-blue-700 mr-2"></i>LinkedIn
                    </label>
                    <input type="url" 
                           id="social_linkedin" 
                           name="social_linkedin" 
                           value="{{ old('social_linkedin', $settings['social_linkedin'] ?? '') }}"
                           placeholder="https://linkedin.com/company/yourcompany"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>

                <!-- Twitter -->
                <div>
                    <label for="social_twitter" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-twitter text-blue-400 mr-2"></i>Twitter
                    </label>
                    <input type="url" 
                           id="social_twitter" 
                           name="social_twitter" 
                           value="{{ old('social_twitter', $settings['social_twitter'] ?? '') }}"
                           placeholder="https://twitter.com/yourhandle"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>

        <!-- Feature Toggles -->
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-6">Feature Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Enable Blog -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="enable_blog" 
                           name="enable_blog" 
                           value="1" 
                           {{ old('enable_blog', $settings['enable_blog'] ?? true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="enable_blog" class="ml-2 block text-sm text-gray-900">
                        Enable Blog Section
                    </label>
                </div>

                <!-- Enable Testimonials -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="enable_testimonials" 
                           name="enable_testimonials" 
                           value="1" 
                           {{ old('enable_testimonials', $settings['enable_testimonials'] ?? true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="enable_testimonials" class="ml-2 block text-sm text-gray-900">
                        Enable Testimonials
                    </label>
                </div>

                <!-- Enable Contact Form -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="enable_contact_form" 
                           name="enable_contact_form" 
                           value="1" 
                           {{ old('enable_contact_form', $settings['enable_contact_form'] ?? true) ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="enable_contact_form" class="ml-2 block text-sm text-gray-900">
                        Enable Contact Form
                    </label>
                </div>

                <!-- Maintenance Mode -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           id="maintenance_mode" 
                           name="maintenance_mode" 
                           value="1" 
                           {{ old('maintenance_mode', $settings['maintenance_mode'] ?? false) ? 'checked' : '' }}
                           class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                    <label for="maintenance_mode" class="ml-2 block text-sm text-gray-900">
                        <span class="text-red-600">Maintenance Mode</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <button type="button" onclick="clearCache()" 
                        class="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                    <i class="fas fa-broom mr-2"></i>
                    Clear Cache
                </button>
            </div>
            
            <button type="submit" 
                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>

    <x-slot name="scripts">
        <script>
            // Logo upload
            document.getElementById('logo_upload').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const formData = new FormData();
                    formData.append('logo', file);
                    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

                    fetch('{{ route("admin.settings.upload-logo") }}', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'Upload failed');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Upload failed');
                    });
                }
            });

            // Clear cache
            function clearCache() {
                if (!confirm('Are you sure you want to clear the cache?')) {
                    return;
                }

                fetch('{{ route("admin.settings.clear-cache") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Cache cleared successfully');
                    } else {
                        alert(data.message || 'Failed to clear cache');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to clear cache');
                });
            }
        </script>
    </x-slot>
</x-admin-layout>
