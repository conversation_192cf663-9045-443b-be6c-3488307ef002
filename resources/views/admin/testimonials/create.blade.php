<x-admin-layout title="Create Testimonial">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.testimonials.index') }}" class="hover:text-blue-600">Testimonials</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Create Testimonial</span>
        </div>
        
        <h1 class="text-2xl font-bold text-gray-900">Create New Testimonial</h1>
        <p class="text-gray-600">Add a new customer testimonial</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow p-6">
                <form method="POST" action="{{ route('admin.testimonials.store') }}" class="space-y-6">
                    @csrf

                    <!-- Client Information -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Client Name -->
                            <div>
                                <label for="client_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    Client Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" 
                                       id="client_name" 
                                       name="client_name" 
                                       value="{{ old('client_name') }}" 
                                       required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('client_name') border-red-500 @enderror">
                                @error('client_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client Title -->
                            <div>
                                <label for="client_title" class="block text-sm font-medium text-gray-700 mb-2">
                                    Client Title/Position
                                </label>
                                <input type="text" 
                                       id="client_title" 
                                       name="client_title" 
                                       value="{{ old('client_title') }}"
                                       placeholder="e.g., CEO, Student, etc."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('client_title') border-red-500 @enderror">
                                @error('client_title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client Company -->
                            <div class="md:col-span-2">
                                <label for="client_company" class="block text-sm font-medium text-gray-700 mb-2">
                                    Company/Organization
                                </label>
                                <input type="text" 
                                       id="client_company" 
                                       name="client_company" 
                                       value="{{ old('client_company') }}"
                                       placeholder="Company or organization name"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('client_company') border-red-500 @enderror">
                                @error('client_company')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Client Image -->
                            <div class="md:col-span-2">
                                <label for="client_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Client Photo URL
                                </label>
                                <input type="url" 
                                       id="client_image" 
                                       name="client_image" 
                                       value="{{ old('client_image') }}"
                                       placeholder="https://example.com/photo.jpg"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('client_image') border-red-500 @enderror">
                                @error('client_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Optional: URL to client's photo</p>
                            </div>
                        </div>
                    </div>

                    <!-- Testimonial Content -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Testimonial Content</h3>
                        
                        <!-- Rating -->
                        <div class="mb-6">
                            <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">
                                Rating <span class="text-red-500">*</span>
                            </label>
                            <select id="rating" 
                                    name="rating" 
                                    required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('rating') border-red-500 @enderror">
                                <option value="">Select Rating</option>
                                <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>⭐⭐⭐⭐⭐ (5 stars)</option>
                                <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>⭐⭐⭐⭐ (4 stars)</option>
                                <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>⭐⭐⭐ (3 stars)</option>
                                <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>⭐⭐ (2 stars)</option>
                                <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>⭐ (1 star)</option>
                            </select>
                            @error('rating')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                Testimonial Content <span class="text-red-500">*</span>
                            </label>
                            <textarea id="content" 
                                      name="content" 
                                      rows="6"
                                      required
                                      placeholder="Enter the client's testimonial..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('content') border-red-500 @enderror">{{ old('content') }}</textarea>
                            @error('content')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-xs text-gray-500">Write the testimonial in the client's own words</p>
                        </div>
                    </div>

                    <!-- Settings -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Display Settings</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Sort Order -->
                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">
                                    Sort Order
                                </label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="{{ old('sort_order', 0) }}"
                                       min="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('sort_order') border-red-500 @enderror">
                                @error('sort_order')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-xs text-gray-500">Lower numbers appear first</p>
                            </div>
                        </div>

                        <!-- Status Options -->
                        <div class="mt-6 space-y-3">
                            <!-- Active -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                    Active Testimonial
                                </label>
                            </div>

                            <!-- Featured -->
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="is_featured" 
                                       name="is_featured" 
                                       value="1" 
                                       {{ old('is_featured', false) ? 'checked' : '' }}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                                    Featured Testimonial
                                </label>
                            </div>
                        </div>
                        
                        <p class="mt-2 text-xs text-gray-500">Featured testimonials appear prominently on the website</p>
                    </div>

                    <!-- Actions -->
                    <div class="border-t border-gray-200 pt-6 flex items-center justify-between">
                        <a href="{{ route('admin.testimonials.index') }}" 
                           class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Cancel
                        </a>
                        
                        <button type="submit" 
                                class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-save mr-2"></i>
                            Create Testimonial
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Tips -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Writing Tips</h3>
                
                <div class="space-y-3 text-sm text-gray-600">
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Use the client's exact words whenever possible</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Include specific details about the service provided</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Highlight the results or benefits achieved</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Keep testimonials authentic and believable</p>
                    </div>
                    
                    <div class="flex items-start space-x-2">
                        <i class="fas fa-lightbulb text-yellow-500 mt-0.5"></i>
                        <p>Include client's title and company for credibility</p>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center space-x-3 mb-4">
                        <div id="preview-avatar" class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        
                        <div>
                            <h4 id="preview-name" class="font-medium text-gray-900">Client Name</h4>
                            <p id="preview-title" class="text-sm text-gray-600">Client Title</p>
                            <p id="preview-company" class="text-xs text-gray-500">Company</p>
                        </div>
                    </div>

                    <div id="preview-rating" class="flex items-center mb-3">
                        <span class="text-gray-400">No rating selected</span>
                    </div>

                    <blockquote id="preview-content" class="text-gray-700 text-sm italic">
                        "Your testimonial content will appear here..."
                    </blockquote>
                </div>
            </div>
        </div>
    </div>

    <x-slot name="scripts">
        <script>
            // Live preview
            function updatePreview() {
                const name = document.getElementById('client_name').value || 'Client Name';
                const title = document.getElementById('client_title').value || 'Client Title';
                const company = document.getElementById('client_company').value || 'Company';
                const content = document.getElementById('content').value || 'Your testimonial content will appear here...';
                const rating = document.getElementById('rating').value;
                const imageUrl = document.getElementById('client_image').value;
                
                document.getElementById('preview-name').textContent = name;
                document.getElementById('preview-title').textContent = title;
                document.getElementById('preview-company').textContent = company;
                document.getElementById('preview-content').textContent = `"${content}"`;
                
                // Update rating
                const ratingContainer = document.getElementById('preview-rating');
                if (rating) {
                    const stars = '⭐'.repeat(parseInt(rating));
                    ratingContainer.innerHTML = `<span class="text-yellow-400">${stars}</span> <span class="text-sm text-gray-600 ml-1">(${rating}/5)</span>`;
                } else {
                    ratingContainer.innerHTML = '<span class="text-gray-400">No rating selected</span>';
                }
                
                // Update avatar
                const avatarContainer = document.getElementById('preview-avatar');
                if (imageUrl) {
                    avatarContainer.innerHTML = `<img src="${imageUrl}" alt="${name}" class="w-12 h-12 rounded-full object-cover">`;
                } else {
                    avatarContainer.innerHTML = '<i class="fas fa-user text-gray-400"></i>';
                }
            }

            // Add event listeners
            document.getElementById('client_name').addEventListener('input', updatePreview);
            document.getElementById('client_title').addEventListener('input', updatePreview);
            document.getElementById('client_company').addEventListener('input', updatePreview);
            document.getElementById('content').addEventListener('input', updatePreview);
            document.getElementById('rating').addEventListener('change', updatePreview);
            document.getElementById('client_image').addEventListener('input', updatePreview);
        </script>
    </x-slot>
</x-admin-layout>
