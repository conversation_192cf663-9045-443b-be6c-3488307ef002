<x-admin-layout title="Testimonials">
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Testimonials</h1>
                <p class="text-gray-600">Manage customer testimonials and reviews</p>
            </div>
            
            @if(auth()->user()->hasPermission('create_testimonials'))
                <a href="{{ route('admin.testimonials.create') }}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    New Testimonial
                </a>
            @endif
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-quote-left text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Testimonials</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $testimonials->total() }}</h3>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $testimonials->where('is_active', true)->count() }}</h3>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-star text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Featured</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $testimonials->where('is_featured', true)->count() }}</h3>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-star-half-alt text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Rating</p>
                    <h3 class="text-lg font-semibold text-gray-900">{{ number_format($testimonials->avg('rating'), 1) }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Grid -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        @if($testimonials->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6" id="testimonials-container">
                @foreach($testimonials as $testimonial)
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow" data-testimonial-id="{{ $testimonial->id }}">
                        <div class="flex items-center justify-between mb-4">
                            @if(auth()->user()->hasPermission('edit_testimonials'))
                                <div class="cursor-move text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                            @endif
                            
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $testimonial->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                    {{ $testimonial->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                
                                @if($testimonial->is_featured)
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-star mr-1"></i>
                                        Featured
                                    </span>
                                @endif
                            </div>
                        </div>

                        <!-- Client Info -->
                        <div class="flex items-center space-x-3 mb-4">
                            @if($testimonial->client_image)
                                <img src="{{ Storage::url($testimonial->client_image) }}" alt="{{ $testimonial->client_name }}" class="w-12 h-12 rounded-full object-cover">
                            @else
                                <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-gray-400"></i>
                                </div>
                            @endif
                            
                            <div>
                                <h3 class="font-medium text-gray-900">{{ $testimonial->client_name }}</h3>
                                @if($testimonial->client_title)
                                    <p class="text-sm text-gray-600">{{ $testimonial->client_title }}</p>
                                @endif
                                @if($testimonial->client_company)
                                    <p class="text-xs text-gray-500">{{ $testimonial->client_company }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Rating -->
                        <div class="flex items-center mb-3">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star text-sm {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }}"></i>
                            @endfor
                            <span class="ml-2 text-sm text-gray-600">({{ $testimonial->rating }}/5)</span>
                        </div>

                        <!-- Content -->
                        <blockquote class="text-gray-700 text-sm leading-relaxed mb-4">
                            "{{ Str::limit($testimonial->content, 120) }}"
                        </blockquote>

                        <!-- Actions -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <div class="text-xs text-gray-500">
                                Order: {{ $testimonial->sort_order }}
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                @if(auth()->user()->hasPermission('view_testimonials'))
                                    <a href="{{ route('admin.testimonials.show', $testimonial) }}" 
                                       class="text-blue-600 hover:text-blue-900" title="View">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                @endif
                                
                                @if(auth()->user()->hasPermission('edit_testimonials'))
                                    <a href="{{ route('admin.testimonials.edit', $testimonial) }}" 
                                       class="text-indigo-600 hover:text-indigo-900" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                @endif
                                
                                @if(auth()->user()->hasPermission('delete_testimonials'))
                                    <form method="POST" action="{{ route('admin.testimonials.destroy', $testimonial) }}" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to delete this testimonial?')"
                                                class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($testimonials->hasPages())
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    {{ $testimonials->links() }}
                </div>
            @endif
        @else
            <div class="p-12 text-center">
                <div class="flex flex-col items-center">
                    <i class="fas fa-quote-left text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No testimonials found</h3>
                    <p class="text-gray-500 mb-4">Get started by adding your first customer testimonial.</p>
                    
                    @if(auth()->user()->hasPermission('create_testimonials'))
                        <a href="{{ route('admin.testimonials.create') }}" 
                           class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Create Testimonial
                        </a>
                    @endif
                </div>
            </div>
        @endif
    </div>

    <x-slot name="scripts">
        @if(auth()->user()->hasPermission('edit_testimonials') && $testimonials->count() > 0)
            <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const container = document.getElementById('testimonials-container');
                    
                    if (container) {
                        const sortable = Sortable.create(container, {
                            handle: '.cursor-move',
                            animation: 150,
                            onEnd: function(evt) {
                                const testimonials = [];
                                const items = container.children;
                                
                                for (let i = 0; i < items.length; i++) {
                                    const testimonialId = items[i].getAttribute('data-testimonial-id');
                                    testimonials.push({
                                        id: parseInt(testimonialId),
                                        sort_order: i
                                    });
                                }
                                
                                fetch('{{ route("admin.testimonials.reorder") }}', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                    },
                                    body: JSON.stringify({ testimonials: testimonials })
                                })
                                .then(response => response.json())
                                .then(data => {
                                    if (!data.success) {
                                        alert('Failed to reorder testimonials');
                                        location.reload();
                                    }
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    alert('Failed to reorder testimonials');
                                    location.reload();
                                });
                            }
                        });
                    }
                });
            </script>
        @endif
    </x-slot>
</x-admin-layout>
