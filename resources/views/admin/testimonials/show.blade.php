<x-admin-layout title="View Testimonial">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.testimonials.index') }}" class="hover:text-blue-600">Testimonials</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ $testimonial->client_name }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $testimonial->client_name }}'s Testimonial</h1>
                <p class="text-gray-600">Testimonial details and information</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_testimonials'))
                    <a href="{{ route('admin.testimonials.edit', $testimonial) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit Testimonial
                    </a>
                @endif
                
                @if(auth()->user()->hasPermission('delete_testimonials'))
                    <form method="POST" action="{{ route('admin.testimonials.destroy', $testimonial) }}" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to delete this testimonial?')"
                                class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Testimonial Display -->
            <div class="bg-white rounded-lg shadow p-8">
                <div class="text-center mb-8">
                    <div class="flex items-center justify-center space-x-2 mb-4">
                        @if($testimonial->is_featured)
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-star mr-1"></i>
                                Featured
                            </span>
                        @endif
                        
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {{ $testimonial->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $testimonial->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>
                    
                    <!-- Client Photo -->
                    <div class="mb-6">
                        @if($testimonial->client_image)
                            <img src="{{ Storage::url($testimonial->client_image) }}" 
                                 alt="{{ $testimonial->client_name }}" 
                                 class="w-24 h-24 rounded-full object-cover mx-auto border-4 border-gray-100">
                        @else
                            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto border-4 border-gray-100">
                                <i class="fas fa-user text-gray-400 text-2xl"></i>
                            </div>
                        @endif
                    </div>
                    
                    <!-- Rating -->
                    <div class="flex items-center justify-center mb-6">
                        @for($i = 1; $i <= 5; $i++)
                            <i class="fas fa-star text-2xl {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }} mr-1"></i>
                        @endfor
                        <span class="ml-2 text-lg text-gray-600">({{ $testimonial->rating }}/5)</span>
                    </div>
                    
                    <!-- Testimonial Content -->
                    <blockquote class="text-xl text-gray-700 leading-relaxed italic mb-8">
                        "{{ $testimonial->content }}"
                    </blockquote>
                    
                    <!-- Client Information -->
                    <div class="border-t border-gray-200 pt-6">
                        <h3 class="text-lg font-semibold text-gray-900">{{ $testimonial->client_name }}</h3>
                        @if($testimonial->client_title)
                            <p class="text-gray-600">{{ $testimonial->client_title }}</p>
                        @endif
                        @if($testimonial->client_company)
                            <p class="text-gray-500">{{ $testimonial->client_company }}</p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Client Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Client Details</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <p class="text-gray-900">{{ $testimonial->client_name }}</p>
                    </div>
                    
                    @if($testimonial->client_title)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Title/Position</label>
                            <p class="text-gray-900">{{ $testimonial->client_title }}</p>
                        </div>
                    @endif
                    
                    @if($testimonial->client_company)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Company/Organization</label>
                            <p class="text-gray-900">{{ $testimonial->client_company }}</p>
                        </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Rating</label>
                        <div class="flex items-center">
                            @for($i = 1; $i <= 5; $i++)
                                <i class="fas fa-star text-sm {{ $i <= $testimonial->rating ? 'text-yellow-400' : 'text-gray-300' }} mr-1"></i>
                            @endfor
                            <span class="ml-2 text-sm text-gray-600">({{ $testimonial->rating }} out of 5)</span>
                        </div>
                    </div>
                    
                    @if($testimonial->client_image)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Photo URL</label>
                            <p class="text-gray-900 text-sm break-all">{{ $testimonial->client_image }}</p>
                        </div>
                    @endif
                </div>
                
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Testimonial Content</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 leading-relaxed">{{ $testimonial->content }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Status</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Active</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $testimonial->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ $testimonial->is_active ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Featured</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {{ $testimonial->is_featured ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $testimonial->is_featured ? 'Yes' : 'No' }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Sort Order</span>
                        <span class="text-sm font-medium text-gray-900">{{ $testimonial->sort_order }}</span>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Metadata</h4>
                
                <div class="space-y-3 text-sm">
                    <div>
                        <label class="block text-gray-600 mb-1">Created</label>
                        <p class="text-gray-900">{{ $testimonial->created_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $testimonial->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $testimonial->updated_at->format('M d, Y H:i') }}</p>
                        <p class="text-gray-500">{{ $testimonial->updated_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-gray-600 mb-1">Testimonial ID</label>
                        <p class="text-gray-900 font-mono">#{{ $testimonial->id }}</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_testimonials'))
                        <a href="{{ route('admin.testimonials.edit', $testimonial) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Testimonial
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.testimonials.index') }}" 
                       class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                        <i class="fas fa-list mr-2"></i>
                        All Testimonials
                    </a>
                    
                    @if(auth()->user()->hasPermission('create_testimonials'))
                        <a href="{{ route('admin.testimonials.create') }}" 
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center block">
                            <i class="fas fa-plus mr-2"></i>
                            New Testimonial
                        </a>
                    @endif
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                
                <div class="space-y-3 text-sm">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Content Length</span>
                        <span class="font-medium text-gray-900">{{ strlen($testimonial->content) }} chars</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Word Count</span>
                        <span class="font-medium text-gray-900">{{ str_word_count($testimonial->content) }} words</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Has Photo</span>
                        <span class="font-medium text-gray-900">{{ $testimonial->client_image ? 'Yes' : 'No' }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-gray-600">Reading Time</span>
                        <span class="font-medium text-gray-900">{{ ceil(str_word_count($testimonial->content) / 200) }} min</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-admin-layout>
