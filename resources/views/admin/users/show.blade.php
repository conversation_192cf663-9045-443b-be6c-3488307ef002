<x-admin-layout title="User Details">
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600 mb-4">
            <a href="{{ route('admin.users.index') }}" class="hover:text-blue-600">Users</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ $user->name }}</span>
        </div>
        
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $user->name }}</h1>
                <p class="text-gray-600">{{ $user->email }}</p>
            </div>
            
            <div class="flex items-center space-x-3">
                @if(auth()->user()->hasPermission('edit_users') && ($user->role->name !== 'super_admin' || auth()->user()->role->name === 'super_admin'))
                    <a href="{{ route('admin.users.edit', $user) }}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Edit User
                    </a>
                @endif
                
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    {{ $user->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                        <p class="text-gray-900">{{ $user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Email Address</label>
                        <p class="text-gray-900">{{ $user->email }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Phone Number</label>
                        <p class="text-gray-900">{{ $user->phone ?: 'Not provided' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Role</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium
                            {{ $user->role->name === 'super_admin' ? 'bg-red-100 text-red-800' : 
                               ($user->role->name === 'admin' ? 'bg-blue-100 text-blue-800' : 
                                ($user->role->name === 'editor' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800')) }}">
                            {{ $user->role->display_name }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Status</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Account Created</label>
                        <p class="text-gray-900">{{ $user->created_at->format('M d, Y') }}</p>
                        <p class="text-sm text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                        <p class="text-gray-900">{{ $user->updated_at->format('M d, Y') }}</p>
                        <p class="text-sm text-gray-500">{{ $user->updated_at->diffForHumans() }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 mb-1">Last Login</label>
                        <p class="text-gray-900">{{ $user->last_login_at ? $user->last_login_at->format('M d, Y H:i') : 'Never' }}</p>
                        <p class="text-sm text-gray-500">{{ $user->last_login_at ? $user->last_login_at->diffForHumans() : 'No login recorded' }}</p>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            @if($recentActivities->count() > 0)
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                    
                    <div class="space-y-4">
                        @foreach($recentActivities as $activity)
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-{{ $activity->action === 'created' ? 'green' : ($activity->action === 'updated' ? 'blue' : ($activity->action === 'deleted' ? 'red' : 'gray')) }}-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-{{ $activity->action === 'created' ? 'plus' : ($activity->action === 'updated' ? 'edit' : ($activity->action === 'deleted' ? 'trash' : 'eye')) }} text-{{ $activity->action === 'created' ? 'green' : ($activity->action === 'updated' ? 'blue' : ($activity->action === 'deleted' ? 'red' : 'gray')) }}-600 text-sm"></i>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm text-gray-900">{{ $activity->description }}</p>
                                    <p class="text-xs text-gray-500">{{ $activity->created_at->diffForHumans() }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- User Avatar -->
            <div class="bg-white rounded-lg shadow p-6 text-center">
                <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="w-24 h-24 rounded-full mx-auto mb-4">
                <h4 class="text-lg font-semibold text-gray-900">{{ $user->name }}</h4>
                <p class="text-sm text-gray-600">{{ $user->role->display_name }}</p>
            </div>

            <!-- Role Permissions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Role Permissions</h4>
                
                @if($user->role->permissions->count() > 0)
                    <div class="space-y-3">
                        @foreach($user->role->permissions->groupBy('group') as $group => $permissions)
                            <div>
                                <h5 class="text-sm font-medium text-gray-700 mb-2">{{ ucfirst(str_replace('_', ' ', $group)) }}</h5>
                                <div class="space-y-1">
                                    @foreach($permissions as $permission)
                                        <div class="flex items-center text-sm text-gray-600">
                                            <i class="fas fa-check text-green-500 mr-2"></i>
                                            {{ $permission->display_name }}
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500">No permissions assigned to this role.</p>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h4>
                
                <div class="space-y-3">
                    @if(auth()->user()->hasPermission('edit_users') && ($user->role->name !== 'super_admin' || auth()->user()->role->name === 'super_admin'))
                        <a href="{{ route('admin.users.edit', $user) }}" 
                           class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
                            <i class="fas fa-edit mr-2"></i>
                            Edit User
                        </a>
                    @endif
                    
                    @if(auth()->user()->hasPermission('edit_users') && $user->role->name !== 'super_admin' && $user->id !== auth()->id())
                        <button onclick="toggleUserStatus({{ $user->id }})" 
                                class="w-full bg-{{ $user->is_active ? 'red' : 'green' }}-600 text-white px-4 py-2 rounded-lg hover:bg-{{ $user->is_active ? 'red' : 'green' }}-700 transition-colors">
                            <i class="fas fa-toggle-{{ $user->is_active ? 'off' : 'on' }} mr-2"></i>
                            {{ $user->is_active ? 'Deactivate' : 'Activate' }} User
                        </button>
                    @endif
                    
                    @if(auth()->user()->hasPermission('view_logs'))
                        <a href="{{ route('admin.activity-logs.index') }}?user={{ $user->id }}" 
                           class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
                            <i class="fas fa-history mr-2"></i>
                            View All Activity
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <x-slot name="scripts">
        <script>
            function toggleUserStatus(userId) {
                if (!confirm('Are you sure you want to toggle this user\'s status?')) {
                    return;
                }

                fetch(`/admin/users/${userId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.error || 'An error occurred');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred');
                });
            }
        </script>
    </x-slot>
</x-admin-layout>
