<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="{ sidebarOpen: false }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ $title ?? 'Admin Panel' }} - Global Ventures Tanzania</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Alpine.js is loaded via Vite in app.js -->

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional head content -->
    {{ $head ?? '' }}
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
             :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">
            
            <!-- Logo -->
            <div class="flex items-center justify-center h-16 px-4 bg-gradient-to-r from-blue-600 to-indigo-700">
                <h1 class="text-xl font-bold text-white">Global Ventures Admin</h1>
            </div>

            <!-- Navigation -->
            <nav class="mt-8 px-4">
                <div class="space-y-2">
                    <!-- Dashboard -->
                    <a href="{{ route('admin.dashboard') }}" 
                       class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.dashboard') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                        <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>

                    @if(auth()->user()->hasPermission('view_users'))
                    <!-- Users -->
                    <a href="{{ route('admin.users.index') }}" 
                       class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.users.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                        <i class="fas fa-users w-5 h-5 mr-3"></i>
                        Users
                    </a>
                    @endif

                    @if(auth()->user()->hasPermission('view_roles'))
                    <!-- Roles -->
                    <a href="{{ route('admin.roles.index') }}" 
                       class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.roles.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                        <i class="fas fa-user-shield w-5 h-5 mr-3"></i>
                        Roles & Permissions
                    </a>
                    @endif

                    <!-- Content Management -->
                    <div class="pt-4">
                        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">Content</h3>
                        
                        @if(auth()->user()->hasPermission('view_services'))
                        <a href="{{ route('admin.services.index') }}" 
                           class="flex items-center px-4 py-3 mt-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.services.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-concierge-bell w-5 h-5 mr-3"></i>
                            Services
                        </a>
                        @endif

                        @if(auth()->user()->hasPermission('view_blog'))
                        <a href="{{ route('admin.blog-posts.index') }}" 
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.blog-posts.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-blog w-5 h-5 mr-3"></i>
                            Blog Posts
                        </a>
                        @endif

                        @if(auth()->user()->hasPermission('view_testimonials'))
                        <a href="{{ route('admin.testimonials.index') }}" 
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.testimonials.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-quote-left w-5 h-5 mr-3"></i>
                            Testimonials
                        </a>
                        @endif

                        @if(auth()->user()->hasPermission('view_faqs'))
                        <a href="{{ route('admin.faqs.index') }}" 
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.faqs.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-question-circle w-5 h-5 mr-3"></i>
                            FAQs
                        </a>
                        @endif
                    </div>

                    <!-- Communications -->
                    <div class="pt-4">
                        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">Communications</h3>
                        
                        @if(auth()->user()->hasPermission('view_inquiries'))
                        <a href="{{ route('admin.inquiries.index') }}"
                           class="flex items-center px-4 py-3 mt-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.inquiries.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-envelope w-5 h-5 mr-3"></i>
                            Contact Inquiries
                            @if($newInquiriesCount = \App\Models\ContactInquiry::new()->count())
                                <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">{{ $newInquiriesCount }}</span>
                            @endif
                        </a>
                        @endif

                        @if(auth()->user()->hasPermission('view_feedback'))
                        <a href="{{ route('admin.feedback.index') }}"
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.feedback.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-comments w-5 h-5 mr-3"></i>
                            Customer Feedback
                            @if($newFeedbackCount = \App\Models\Feedback::new()->count())
                                <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">{{ $newFeedbackCount }}</span>
                            @endif
                        </a>
                        @endif
                    </div>

                    <!-- System -->
                    <div class="pt-4">
                        <h3 class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider">System</h3>
                        
                        @if(auth()->user()->hasPermission('view_settings'))
                        <a href="{{ route('admin.settings.index') }}" 
                           class="flex items-center px-4 py-3 mt-2 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.settings.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-cog w-5 h-5 mr-3"></i>
                            Settings
                        </a>
                        @endif

                        @if(auth()->user()->hasPermission('view_logs'))
                        <a href="{{ route('admin.activity-logs.index') }}" 
                           class="flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors {{ request()->routeIs('admin.activity-logs.*') ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : '' }}">
                            <i class="fas fa-history w-5 h-5 mr-3"></i>
                            Activity Logs
                        </a>
                        @endif
                    </div>
                </div>
            </nav>

            <!-- User Info -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <img src="{{ auth()->user()->avatar_url }}" alt="{{ auth()->user()->name }}" class="w-8 h-8 rounded-full">
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-700">{{ auth()->user()->name }}</p>
                        <p class="text-xs text-gray-500">{{ auth()->user()->role->display_name ?? 'User' }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile sidebar overlay -->
        <div x-show="sidebarOpen" 
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
             @click="sidebarOpen = false"></div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col lg:ml-0">
            <!-- Top bar -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-4 py-4">
                    <div class="flex items-center">
                        <button @click="sidebarOpen = !sidebarOpen" class="text-gray-500 hover:text-gray-700 lg:hidden">
                            <i class="fas fa-bars w-6 h-6"></i>
                        </button>
                        <h1 class="ml-4 text-2xl font-semibold text-gray-900 lg:ml-0">{{ $title ?? 'Admin Panel' }}</h1>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- View Site -->
                        <a href="{{ route('home') }}" target="_blank" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-external-link-alt w-5 h-5"></i>
                        </a>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-gray-700 hover:text-gray-900">
                                <img src="{{ auth()->user()->avatar_url }}" alt="{{ auth()->user()->name }}" class="w-8 h-8 rounded-full">
                                <i class="fas fa-chevron-down w-4 h-4 ml-2"></i>
                            </button>

                            <div x-show="open" 
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 @click.away="open = false"
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                
                                <a href="{{ route('admin.profile.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user w-4 h-4 mr-2"></i>
                                    Profile
                                </a>
                                @if(auth()->user()->hasPermission('view_settings'))
                                    <a href="{{ route('admin.settings.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-cog w-4 h-4 mr-2"></i>
                                        Settings
                                    </a>
                                @endif
                                <div class="border-t border-gray-100"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt w-4 h-4 mr-2"></i>
                                        Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page content -->
            <main class="flex-1 overflow-y-auto">
                <!-- Flash messages -->
                @if(session('success'))
                    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md m-4" x-data="{ show: true }" x-show="show" x-transition>
                        <div class="flex items-center justify-between">
                            <span>{{ session('success') }}</span>
                            <button @click="show = false" class="text-green-500 hover:text-green-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md m-4" x-data="{ show: true }" x-show="show" x-transition>
                        <div class="flex items-center justify-between">
                            <span>{{ session('error') }}</span>
                            <button @click="show = false" class="text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                @endif

                <!-- Main content area -->
                <div class="p-6">
                    {{ $slot }}
                </div>
            </main>
        </div>
    </div>

    <!-- Additional scripts -->
    {{ $scripts ?? '' }}
</body>
</html>
