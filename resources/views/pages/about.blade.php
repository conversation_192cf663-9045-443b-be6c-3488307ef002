<x-layout title="About Us - Global Ventures Tanzania">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                About Global Ventures Tanzania
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                Your trusted partner for travel, study abroad, and visa assistance services.
            </p>
        </div>
    </section>

    <!-- Mission & Vision Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div x-data="{ inView: false }" x-intersect="inView = true">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-x-10" x-transition:enter-end="opacity-100 transform translate-x-0">
                        Our Mission
                    </h2>
                    <p class="text-lg text-gray-600 mb-6" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-x-10" x-transition:enter-end="opacity-100 transform translate-x-0">
                        To connect individuals with global opportunities in travel, education, and work. We believe in making dreams accessible and providing comprehensive support throughout your journey.
                    </p>
                    <p class="text-lg text-gray-600" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-400" x-transition:enter-start="opacity-0 transform translate-x-10" x-transition:enter-end="opacity-100 transform translate-x-0">
                        Our commitment to excellence ensures that we provide personalized service and expert guidance to help you achieve your goals.
                    </p>
                </div>
                <div class="bg-gradient-to-br from-blue-50 to-indigo-100 p-8 rounded-2xl" x-data="{ inView: false }" x-intersect="inView = true">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        Our Vision
                    </h3>
                    <p class="text-gray-700 mb-6" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        To be the most trusted travel and study-abroad facilitator in East Africa, recognized for our expertise, integrity, and commitment to client success.
                    </p>
                    <div class="space-y-3" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-400" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="text-gray-700">Innovation-driven approach</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="text-gray-700">Client-centric solutions</span>
                        </div>
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="text-gray-700">Sustainable growth focus</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    These principles guide everything we do and shape our company culture.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-blue-200 transition-colors" x-show="inView" x-transition:enter="transition ease-out duration-600" x-transition:enter-start="opacity-0 transform scale-0" x-transition:enter-end="opacity-100 transform scale-100">
                        <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Integrity</h3>
                    <p class="text-gray-600 text-sm">Honest, transparent, and ethical in all our dealings</p>
                </div>

                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-green-200 transition-colors" x-show="inView" x-transition:enter="transition ease-out duration-600 delay-200" x-transition:enter-start="opacity-0 transform scale-0" x-transition:enter-end="opacity-100 transform scale-100">
                        <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Innovation</h3>
                    <p class="text-gray-600 text-sm">Constantly pushing boundaries and embracing new ideas</p>
                </div>

                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-purple-200 transition-colors" x-show="inView" x-transition:enter="transition ease-out duration-600 delay-400" x-transition:enter-start="opacity-0 transform scale-0" x-transition:enter-end="opacity-100 transform scale-100">
                        <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Collaboration</h3>
                    <p class="text-gray-600 text-sm">Working together to achieve extraordinary results</p>
                </div>

                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-yellow-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-yellow-200 transition-colors" x-show="inView" x-transition:enter="transition ease-out duration-600 delay-600" x-transition:enter-start="opacity-0 transform scale-0" x-transition:enter-end="opacity-100 transform scale-100">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Excellence</h3>
                    <p class="text-gray-600 text-sm">Striving for the highest quality in everything we do</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Meet Our Leadership</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Led by experienced professionals dedicated to helping you achieve your dreams.
                </p>
            </div>

            <div class="flex justify-center">
                <div class="text-center group max-w-md" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="relative mb-6" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="w-40 h-40 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full mx-auto mb-6 flex items-center justify-center text-white text-3xl font-bold">
                            AM
                        </div>
                        <h3 class="text-2xl font-semibold text-gray-900 mb-2">Aidan Mtungi</h3>
                        <p class="text-blue-600 font-medium mb-4 text-lg">CEO & Founder</p>
                        <p class="text-gray-600">
                            Visionary leader with extensive experience in travel and education services. Aidan is passionate about connecting people with global opportunities and has helped hundreds of clients achieve their travel and study abroad goals.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Core Values</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    The principles that guide everything we do at Global Ventures Tanzania.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Integrity -->
                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-200 transition-all duration-300 group-hover:scale-110"
                         x-show="inView"
                         x-transition:enter="transition ease-out duration-600"
                         x-transition:enter-start="opacity-0 transform translate-y-10"
                         x-transition:enter-end="opacity-100 transform translate-y-0">
                        <svg class="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Integrity</h3>
                    <p class="text-gray-600">
                        We maintain the highest standards of honesty and transparency in all our dealings with clients.
                    </p>
                </div>

                <!-- Excellence -->
                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-200 transition-all duration-300 group-hover:scale-110"
                         x-show="inView"
                         x-transition:enter="transition ease-out duration-600 delay-200"
                         x-transition:enter-start="opacity-0 transform translate-y-10"
                         x-transition:enter-end="opacity-100 transform translate-y-0">
                        <svg class="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Excellence</h3>
                    <p class="text-gray-600">
                        We strive for excellence in every service we provide, ensuring the best outcomes for our clients.
                    </p>
                </div>

                <!-- Innovation -->
                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-200 transition-all duration-300 group-hover:scale-110"
                         x-show="inView"
                         x-transition:enter="transition ease-out duration-600 delay-400"
                         x-transition:enter-start="opacity-0 transform translate-y-10"
                         x-transition:enter-end="opacity-100 transform translate-y-0">
                        <svg class="h-10 w-10 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Innovation</h3>
                    <p class="text-gray-600">
                        We continuously innovate our processes to provide better, faster, and more efficient services.
                    </p>
                </div>

                <!-- Support -->
                <div class="text-center group" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-yellow-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-yellow-200 transition-all duration-300 group-hover:scale-110"
                         x-show="inView"
                         x-transition:enter="transition ease-out duration-600 delay-600"
                         x-transition:enter-start="opacity-0 transform translate-y-10"
                         x-transition:enter-end="opacity-100 transform translate-y-0">
                        <svg class="h-10 w-10 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Support</h3>
                    <p class="text-gray-600">
                        We provide comprehensive support throughout your entire journey, from start to finish.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Journey?</h2>
            <p class="text-xl mb-8 text-blue-100">
                Let us help you achieve your travel and education dreams. Contact us today to get started.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    Contact Us
                </a>
                <a href="{{ route('services') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-700 transform hover:scale-105 transition-all duration-200">
                    View Our Services
                </a>
            </div>
        </div>
    </section>
</x-layout>
