<x-layout title="Blog - Global Ventures Tanzania">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Our Blog
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Stay updated with travel tips, study abroad guides, and visa information.
            </p>
        </div>
    </section>

    <!-- Featured Post -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Article</h2>
            </div>
            
            <div class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl overflow-hidden shadow-2xl" x-data="{ inView: false }" x-intersect="inView = true">
                <div class="grid grid-cols-1 lg:grid-cols-2" x-show="inView" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                    <div class="p-8 lg:p-12 text-white">
                        <div class="mb-4">
                            <span class="bg-yellow-400 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">Featured</span>
                        </div>
                        <h3 class="text-3xl font-bold mb-4">How to Prepare for Studying Abroad</h3>
                        <p class="text-blue-100 mb-6 text-lg">
                            A comprehensive guide to preparing for your international education journey, from application to arrival.
                        </p>
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                                <span class="text-sm font-semibold">GT</span>
                            </div>
                            <div>
                                <p class="font-semibold">Global Ventures Team</p>
                                <p class="text-blue-200 text-sm">March 15, 2024</p>
                            </div>
                        </div>
                        <a href="{{ route('blog.single', 'how-to-prepare-for-studying-abroad') }}" class="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors inline-block">
                            Read Full Article
                        </a>
                    </div>
                    <div class="bg-gradient-to-br from-blue-400 to-indigo-600 p-8 lg:p-12 flex items-center justify-center">
                        <div class="text-center">
                            <svg class="h-32 w-32 text-white mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                            </svg>
                            <p class="text-white text-lg">Digital Innovation</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Posts Grid -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Latest Articles</h2>
                <p class="text-xl text-gray-600">Discover insights, tips, and industry news</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Blog Post 1 -->
                <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br from-green-400 to-blue-500 h-48 flex items-center justify-center">
                            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div class="mb-3">
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">Technology</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.single', 'visa-application-tips') }}">Essential Visa Application Tips for Students</a>
                            </h3>
                            <p class="text-gray-600 mb-4">
                                Learn the key steps and requirements for a successful student visa application process.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-white text-xs font-semibold">GT</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">Global Ventures Team</p>
                                        <p class="text-xs text-gray-500">March 10, 2024</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">5 min read</span>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 2 -->
                <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br from-purple-400 to-pink-500 h-48 flex items-center justify-center">
                            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div class="mb-3">
                                <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-semibold">Analytics</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.single', 'best-travel-destinations-2024') }}">Top Travel Destinations for 2024</a>
                            </h3>
                            <p class="text-gray-600 mb-4">
                                Discover the most exciting and affordable travel destinations to visit this year.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-white text-xs font-semibold">GT</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">Global Ventures Team</p>
                                        <p class="text-xs text-gray-500">March 8, 2024</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">7 min read</span>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 3 -->
                <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-400" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br from-yellow-400 to-orange-500 h-48 flex items-center justify-center">
                            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div class="mb-3">
                                <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-semibold">Security</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.single', 'studying-in-canada-guide') }}">Complete Guide to Studying in Canada</a>
                            </h3>
                            <p class="text-gray-600 mb-4">
                                Everything you need to know about pursuing higher education in Canada's top universities.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-white text-xs font-semibold">GT</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">Global Ventures Team</p>
                                        <p class="text-xs text-gray-500">March 5, 2024</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">6 min read</span>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 4 -->
                <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-600" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br from-red-400 to-pink-500 h-48 flex items-center justify-center">
                            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div class="mb-3">
                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold">Mobile</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.single', 'budget-travel-tips') }}">Budget Travel Tips for Students</a>
                            </h3>
                            <p class="text-gray-600 mb-4">
                                Smart strategies to travel the world on a student budget without compromising on experience.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-white text-xs font-semibold">GT</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">Global Ventures Team</p>
                                        <p class="text-xs text-gray-500">March 3, 2024</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">4 min read</span>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 5 -->
                <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br from-indigo-400 to-blue-500 h-48 flex items-center justify-center">
                            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div class="mb-3">
                                <span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-semibold">Strategy</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.single', 'usa-university-application-guide') }}">How to Apply to US Universities: Step-by-Step Guide</a>
                            </h3>
                            <p class="text-gray-600 mb-4">
                                A comprehensive guide to navigating the US university application process successfully.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-white text-xs font-semibold">GT</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">Global Ventures Team</p>
                                        <p class="text-xs text-gray-500">March 1, 2024</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">8 min read</span>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- Blog Post 6 -->
                <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br from-teal-400 to-green-500 h-48 flex items-center justify-center">
                            <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <div class="mb-3">
                                <span class="bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm font-semibold">Performance</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                <a href="{{ route('blog.single', 'cultural-adaptation-tips') }}">Cultural Adaptation Tips for International Students</a>
                            </h3>
                            <p class="text-gray-600 mb-4">
                                Essential advice for adapting to new cultures and making the most of your study abroad experience.
                            </p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center mr-2">
                                        <span class="text-white text-xs font-semibold">GT</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900">Global Ventures Team</p>
                                        <p class="text-xs text-gray-500">February 28, 2024</p>
                                    </div>
                                </div>
                                <span class="text-sm text-gray-500">10 min read</span>
                            </div>
                        </div>
                    </div>
                </article>
            </div>

            <!-- Pagination -->
            <div class="mt-12 flex justify-center">
                <nav class="flex space-x-2">
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colors">Previous</button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg">1</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colors">2</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colors">3</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colors">Next</button>
                </nav>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Stay Updated</h2>
            <p class="text-xl mb-8 text-blue-100">
                Subscribe to our newsletter for the latest insights and industry updates.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-400">
                <button class="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                    Subscribe
                </button>
            </div>
        </div>
    </section>
</x-layout>
