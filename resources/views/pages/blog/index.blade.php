<x-layout title="Blog - Global Ventures Tanzania">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Our Blog
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Stay updated with travel tips, study abroad guides, and visa information.
            </p>
        </div>
    </section>

    <!-- Featured Post -->
    @if($featuredPost)
        <section class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Article</h2>
                </div>

                <div class="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl overflow-hidden shadow-2xl" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="grid grid-cols-1 lg:grid-cols-2" x-show="inView" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        <div class="p-8 lg:p-12 text-white">
                            <div class="mb-4">
                                <span class="bg-yellow-400 text-gray-900 px-3 py-1 rounded-full text-sm font-semibold">Featured</span>
                                @if($featuredPost->category)
                                    <span class="bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm font-semibold ml-2">{{ ucfirst($featuredPost->category) }}</span>
                                @endif
                            </div>
                            <h3 class="text-3xl font-bold mb-4">{{ $featuredPost->title }}</h3>
                            <p class="text-blue-100 mb-6 text-lg">
                                {{ $featuredPost->excerpt }}
                            </p>
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-sm font-semibold">{{ substr($featuredPost->author->name ?? 'GT', 0, 2) }}</span>
                                </div>
                                <div>
                                    <p class="font-semibold">{{ $featuredPost->author->name ?? 'Global Ventures Team' }}</p>
                                    <p class="text-blue-200 text-sm">{{ $featuredPost->formatted_published_date }}</p>
                                </div>
                            </div>
                            <a href="{{ route('blog.single', $featuredPost->slug) }}" class="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors inline-block">
                                Read Full Article
                            </a>
                        </div>
                        <div class="bg-gradient-to-br from-blue-400 to-indigo-600 p-8 lg:p-12 flex items-center justify-center">
                            @if($featuredPost->featured_image_url)
                                <img src="{{ $featuredPost->featured_image_url }}" alt="{{ $featuredPost->title }}" class="w-full h-full object-cover rounded-lg">
                            @else
                                <div class="text-center">
                                    <svg class="h-32 w-32 text-white mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                    </svg>
                                    <p class="text-white text-lg">{{ ucfirst($featuredPost->category ?? 'Article') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif

    <!-- Blog Posts Grid -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Latest Articles</h2>
                <p class="text-xl text-gray-600">Discover insights, tips, and industry news</p>
            </div>

            @if($blogPosts->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($blogPosts as $index => $post)
                        <article class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden" x-data="{ inView: false }" x-intersect="inView = true">
                            <div x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0" style="transition-delay: {{ $index * 100 }}ms">
                                @if($post->featured_image_url)
                                    <div class="h-48 overflow-hidden">
                                        <img src="{{ $post->featured_image_url }}" alt="{{ $post->title }}" class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                                    </div>
                                @else
                                    <div class="bg-gradient-to-br from-blue-400 to-indigo-500 h-48 flex items-center justify-center">
                                        <svg class="h-16 w-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                        </svg>
                                    </div>
                                @endif
                                <div class="p-6">
                                    @if($post->category)
                                        <div class="mb-3">
                                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-semibold">{{ ucfirst($post->category) }}</span>
                                        </div>
                                    @endif
                                    <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors">
                                        <a href="{{ route('blog.single', $post->slug) }}">{{ $post->title }}</a>
                                    </h3>
                                    <p class="text-gray-600 mb-4">
                                        {{ $post->excerpt }}
                                    </p>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                                                <span class="text-white text-xs font-semibold">{{ substr($post->author->name ?? 'GT', 0, 2) }}</span>
                                            </div>
                                            <div>
                                                <p class="text-sm font-semibold text-gray-900">{{ $post->author->name ?? 'Global Ventures Team' }}</p>
                                                <p class="text-xs text-gray-500">{{ $post->formatted_published_date }}</p>
                                            </div>
                                        </div>
                                        <span class="text-sm text-gray-500">{{ $post->reading_time }} min read</span>
                                    </div>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($blogPosts->hasPages())
                    <div class="mt-12 flex justify-center">
                        {{ $blogPosts->links() }}
                    </div>
                @endif
            @else
                <div class="text-center py-12">
                    <div class="bg-white rounded-xl shadow-lg p-8 max-w-md mx-auto">
                        <svg class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                        </svg>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Blog Posts Yet</h3>
                        <p class="text-gray-600">We're working on creating great content for you. Please check back soon!</p>
                    </div>
                </div>
            @endif

        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Stay Updated</h2>
            <p class="text-xl mb-8 text-blue-100">
                Subscribe to our newsletter for the latest insights and industry updates.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-yellow-400">
                <button class="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                    Subscribe
                </button>
            </div>
        </div>
    </section>
</x-layout>
