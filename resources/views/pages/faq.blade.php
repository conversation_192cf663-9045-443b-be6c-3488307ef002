<x-layout title="FAQ - Frequently Asked Questions">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Frequently Asked Questions
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Find answers to common questions about our services and processes.
            </p>
        </div>
    </section>
    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="space-y-6" x-data="{ openFaq: null }">
                <!-- FAQ 1 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <button @click="openFaq = openFaq === 1 ? null : 1" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span class="font-semibold text-gray-900 text-lg">Who developed this website?</span>
                    <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === 1 }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                    </button>
                    <div x-show="openFaq === 1" x-transition class="px-8 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        This website was developed by a talented team: <strong>Frontend by Erick Msilanga</strong> and <strong>Backend by Kadilana Mbogo</strong>. Together, they created a modern, user-friendly platform to serve our clients better.
                    </p>
                    </div>
                </div>
                <!-- FAQ 2 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <button @click="openFaq = openFaq === 2 ? null : 2" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span class="font-semibold text-gray-900 text-lg">Where are you located?</span>
                    <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === 2 }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                    </button>
                    <div x-show="openFaq === 2" x-transition class="px-8 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        We are located at <strong>Victoria House Building, New Bagamoyo Road, 8th Floor, Wing B, Office 04, Dar es Salaam, Tanzania</strong>. You can visit us during business hours or contact us to schedule an appointment.
                    </p>
                    </div>
                </div>
                <!-- FAQ 3 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <button @click="openFaq = openFaq === 3 ? null : 3" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span class="font-semibold text-gray-900 text-lg">What services do you offer?</span>
                    <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === 3 }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                    </button>
                    <div x-show="openFaq === 3" x-transition class="px-8 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        We offer comprehensive travel and education services including air ticketing, travel packages, visa facilitation, custom itineraries, and study abroad programs for USA, India, China, and Canada.
                    </p>
                    </div>
                </div>
                <!-- FAQ 4 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <button @click="openFaq = openFaq === 4 ? null : 4" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span class="font-semibold text-gray-900 text-lg">How can I apply for a visa?</span>
                    <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === 4 }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                    </button>
                    <div x-show="openFaq === 4" x-transition class="px-8 pb-6">
                    <p class="text-gray-600 leading-relaxed">
                        Our visa facilitation service covers everything from document preparation to embassy appointments and interview preparation. Contact us to discuss your specific visa requirements and we'll guide you through the entire process.
                    </p>
                    </div>
                </div>
                <!-- FAQ 5 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <button @click="openFaq = openFaq === 5 ? null : 5" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                        <span class="font-semibold text-gray-900 text-lg">What countries do you help with for study abroad?</span>
                        <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === 5 }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 5" x-transition class="px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            We specialize in study abroad programs for four main destinations: <strong>USA</strong> (top universities with comprehensive support), <strong>India</strong> (affordable tech and medical programs), <strong>China</strong> (modern institutions with scholarships), and <strong>Canada</strong> (multicultural environment with research opportunities).
                        </p>
                    </div>
                    </div>
                </div>
                <!-- FAQ 6 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <button @click="openFaq = openFaq === 6 ? null : 6" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                        <span class="font-semibold text-gray-900 text-lg">How do I book a flight through your service?</span>
                        <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === 6 }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div x-show="openFaq === 6" x-transition class="px-8 pb-6">
                        <p class="text-gray-600 leading-relaxed">
                            Simply contact us with your travel details including destination, dates, and preferences. Our team will search for the best available rates for both domestic and international flights and provide you with options that fit your budget and schedule.
                        </p>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact CTA -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Still Have Questions?</h2>
            <p class="text-xl mb-8 text-blue-100">
                Our team is here to help. Contact us for personalized assistance with your travel and education needs.
            </p>
            <a href="{{ route('contact') }}" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transform hover:scale-105 transition-all duration-200 shadow-lg inline-block">
                Contact Us
            </a>
        </div>
    </section>
</x-layout>
