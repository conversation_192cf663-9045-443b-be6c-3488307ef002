<x-layout title="FAQ - Frequently Asked Questions">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Frequently Asked Questions
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Find answers to common questions about our services and processes.
            </p>
        </div>
    </section>
    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($faqs->count() > 0)
                @foreach($faqs as $category => $categoryFaqs)
                    @if($category)
                        <div class="mb-12">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6 capitalize">{{ str_replace('_', ' ', $category) }}</h2>
                            <div class="space-y-4" x-data="{ openFaq: null }">
                                @foreach($categoryFaqs as $index => $faq)
                                    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                                        <button @click="openFaq = openFaq === '{{ $category }}_{{ $index }}' ? null : '{{ $category }}_{{ $index }}'" class="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                                            <span class="font-semibold text-gray-900 text-lg">{{ $faq->question }}</span>
                                            <svg class="h-6 w-6 text-gray-500 transform transition-transform" :class="{ 'rotate-180': openFaq === '{{ $category }}_{{ $index }}' }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>
                                        <div x-show="openFaq === '{{ $category }}_{{ $index }}'" x-transition class="px-8 pb-6">
                                            <div class="text-gray-600 leading-relaxed prose max-w-none">
                                                {!! nl2br(e($faq->answer)) !!}
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                @endforeach
            @else
                <div class="text-center py-12">
                    <div class="bg-white rounded-xl shadow-lg p-8">
                        <svg class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No FAQs Available</h3>
                        <p class="text-gray-600">We're working on adding frequently asked questions. Please check back soon or contact us directly.</p>
                        <a href="{{ route('contact') }}" class="inline-block mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Contact Us
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Still Have Questions?</h2>
            <p class="text-xl text-gray-600 mb-8">Can't find what you're looking for? We're here to help!</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    Contact Us
                </a>
                <a href="{{ route('feedback.create') }}" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                    Send Feedback
                </a>
            </div>
        </div>
    </section>
</x-layout>
