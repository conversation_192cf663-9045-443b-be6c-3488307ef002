<x-layout title="Feedback - Global Ventures Tanzania">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Share Your Feedback
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Your feedback helps us improve our services and better serve our clients. We value your opinion and appreciate your time.
            </p>
        </div>
    </section>

    <!-- Feedback Form Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-8" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white rounded-2xl shadow-xl p-8" x-data="{ inView: false }" x-intersect="inView = true">
                <h2 class="text-3xl font-bold text-gray-900 mb-6" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                    We'd Love to Hear From You
                </h2>
                
                <form action="{{ route('feedback.store') }}" method="POST" class="space-y-6" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                    @csrf
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('name') border-red-500 @enderror" 
                                   placeholder="Your full name">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('email') border-red-500 @enderror" 
                                   placeholder="<EMAIL>">
                            @error('email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('phone') border-red-500 @enderror" 
                               placeholder="+255 XXX XXX XXX">
                        @error('phone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Feedback Type *</label>
                            <select id="type" name="type" required 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('type') border-red-500 @enderror">
                                <option value="">Select feedback type</option>
                                <option value="general" {{ old('type') === 'general' ? 'selected' : '' }}>General Feedback</option>
                                <option value="complaint" {{ old('type') === 'complaint' ? 'selected' : '' }}>Complaint</option>
                                <option value="suggestion" {{ old('type') === 'suggestion' ? 'selected' : '' }}>Suggestion</option>
                                <option value="compliment" {{ old('type') === 'compliment' ? 'selected' : '' }}>Compliment</option>
                                <option value="inquiry" {{ old('type') === 'inquiry' ? 'selected' : '' }}>Inquiry</option>
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="service_type" class="block text-sm font-medium text-gray-700 mb-2">Service Related To</label>
                            <select id="service_type" name="service_type" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('service_type') border-red-500 @enderror">
                                <option value="">Select service (optional)</option>
                                <option value="air-ticketing" {{ old('service_type') === 'air-ticketing' ? 'selected' : '' }}>Air Ticketing</option>
                                <option value="travel-packages" {{ old('service_type') === 'travel-packages' ? 'selected' : '' }}>Travel Packages</option>
                                <option value="visa-facilitation" {{ old('service_type') === 'visa-facilitation' ? 'selected' : '' }}>Visa Facilitation</option>
                                <option value="custom-itineraries" {{ old('service_type') === 'custom-itineraries' ? 'selected' : '' }}>Custom Itineraries</option>
                                <option value="study-abroad-usa" {{ old('service_type') === 'study-abroad-usa' ? 'selected' : '' }}>Study Abroad - USA</option>
                                <option value="study-abroad-india" {{ old('service_type') === 'study-abroad-india' ? 'selected' : '' }}>Study Abroad - India</option>
                                <option value="study-abroad-china" {{ old('service_type') === 'study-abroad-china' ? 'selected' : '' }}>Study Abroad - China</option>
                                <option value="study-abroad-canada" {{ old('service_type') === 'study-abroad-canada' ? 'selected' : '' }}>Study Abroad - Canada</option>
                            </select>
                            @error('service_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                        <input type="text" id="subject" name="subject" value="{{ old('subject') }}" required 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('subject') border-red-500 @enderror" 
                               placeholder="Brief subject of your feedback">
                        @error('subject')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="rating" class="block text-sm font-medium text-gray-700 mb-2">Overall Rating</label>
                        <div class="flex items-center space-x-2">
                            <select id="rating" name="rating" 
                                    class="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('rating') border-red-500 @enderror">
                                <option value="">Rate our service (optional)</option>
                                <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>⭐⭐⭐⭐⭐ Excellent (5/5)</option>
                                <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>⭐⭐⭐⭐ Good (4/5)</option>
                                <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>⭐⭐⭐ Average (3/5)</option>
                                <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>⭐⭐ Poor (2/5)</option>
                                <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>⭐ Very Poor (1/5)</option>
                            </select>
                        </div>
                        @error('rating')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Your Message *</label>
                        <textarea id="message" name="message" rows="6" required 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 @error('message') border-red-500 @enderror" 
                                  placeholder="Please share your detailed feedback, suggestions, or concerns...">{{ old('message') }}</textarea>
                        @error('message')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-600">
                            <span class="text-red-500">*</span> Required fields
                        </p>
                        <button type="submit" class="bg-blue-600 text-white py-4 px-8 rounded-lg font-semibold hover:bg-blue-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Submit Feedback
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Why Your Feedback Matters Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Your Feedback Matters</h2>
                <p class="text-xl text-gray-600">Your input helps us continuously improve our services</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        <i class="fas fa-chart-line text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">Service Improvement</h3>
                    <p class="text-gray-600" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-400" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">We use your feedback to identify areas for improvement and enhance our service quality.</p>
                </div>
                
                <div class="text-center" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        <i class="fas fa-users text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">Customer Satisfaction</h3>
                    <p class="text-gray-600" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-400" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">Your satisfaction is our priority. We address concerns promptly and work to exceed expectations.</p>
                </div>
                
                <div class="text-center" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        <i class="fas fa-lightbulb text-purple-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">Innovation</h3>
                    <p class="text-gray-600" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-400" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">Your suggestions inspire new features and services that better meet your needs.</p>
                </div>
            </div>
        </div>
    </section>
</x-layout>
