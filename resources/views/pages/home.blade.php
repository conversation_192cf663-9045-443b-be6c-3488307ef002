<x-layout title="Home - Global Ventures Tanzania">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="text-center lg:text-left" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight"
                        x-show="show"
                        x-transition:enter="transition ease-out duration-1000"
                        x-transition:enter-start="opacity-0 transform translate-y-10"
                        x-transition:enter-end="opacity-100 transform translate-y-0">
                        Your Gateway To a World of <span class="text-yellow-400">Opportunities</span>
                    </h1>
                    <p class="text-xl md:text-2xl mb-8 text-blue-100"
                       x-show="show"
                       x-transition:enter="transition ease-out duration-1000 delay-300"
                       x-transition:enter-start="opacity-0 transform translate-y-10"
                       x-transition:enter-end="opacity-100 transform translate-y-0">
                        Travel | Work Abroad | Visa Assistance
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start" 
                         x-show="show" 
                         x-transition:enter="transition ease-out duration-1000 delay-600" 
                         x-transition:enter-start="opacity-0 transform translate-y-10" 
                         x-transition:enter-end="opacity-100 transform translate-y-0">
                        <a href="{{ route('services') }}" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transform hover:scale-105 transition-all duration-200 shadow-lg">
                            Explore Services
                        </a>
                        <a href="{{ route('contact') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-700 transform hover:scale-105 transition-all duration-200">
                            Contact Us
                        </a>
                    </div>
                </div>
                <div class="relative" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)">
                    <div class="bg-white/5 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border border-white/10"
                         x-show="show" 
                         x-transition:enter="transition ease-out duration-1000" 
                         x-transition:enter-start="opacity-0 transform translate-x-10" 
                         x-transition:enter-end="opacity-100 transform translate-x-0">
                        <div class="space-y-6">
                            <div class="flex items-center space-x-4">
                                <div class="bg-white/10 border border-white/20 p-3 rounded-full">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Global Travel</h3>
                                    <p class="text-white/80">Worldwide destinations at your fingertips</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="bg-white/10 border border-white/20 p-3 rounded-full">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Study Abroad</h3>
                                    <p class="text-white/80">Top universities worldwide</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="bg-white/10 border border-white/20 p-3 rounded-full">
                                    <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-white">Visa Support</h3>
                                    <p class="text-white/80">Complete documentation assistance</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Animated background elements -->
        <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-yellow-400 rounded-full opacity-10 animate-pulse"></div>
            <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-300 rounded-full opacity-10 animate-pulse delay-1000"></div>
        </div>
    </section>

    <!-- Service Highlights Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Services</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Comprehensive solutions to help you achieve your travel and education goals.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{{ min(4, $featuredServices->count()) }} gap-8">
                @forelse($featuredServices as $service)
                <div class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group border border-gray-100" x-data="{ inView: false }" x-intersect="inView = true">
                    <div class="bg-gradient-to-br {{ $service->gradient_class }} h-32 flex items-center justify-center" x-show="inView" x-transition:enter="transition ease-out duration-600 delay-{{ $loop->index * 150 }}" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100">
                        @if($service->icon)
                            <i class="{{ $service->icon }} text-4xl text-white"></i>
                        @else
                            <svg class="h-14 w-14 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        @endif
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $service->title }}</h3>
                        <p class="text-gray-600 mb-4">{{ Str::limit($service->description, 80) }}</p>
                        <a href="{{ route('services') }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold">Learn more →</a>
                    </div>
                </div>
                @empty
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-500">No featured services available at the moment.</p>
                </div>
                @endforelse
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Hear from those who have achieved their dreams with our help.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{{ min(3, $testimonials->count()) }} gap-8">
                @forelse($testimonials as $testimonial)
                <div class="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-{{ $loop->index * 200 }}" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="flex text-yellow-400 mb-4">
                            {!! $testimonial->star_rating !!}
                        </div>
                        <p class="text-gray-700 italic mb-6 text-lg leading-relaxed">
                            "{{ $testimonial->content }}"
                        </p>
                        <div class="flex items-center">
                            <img src="{{ $testimonial->avatar_url }}" alt="{{ $testimonial->name }}" class="w-14 h-14 rounded-full object-cover mr-4 border-2 border-blue-200">
                            <div>
                                <p class="font-semibold text-gray-900 text-lg">{{ $testimonial->name }}</p>
                                <p class="text-blue-600 text-sm font-medium">{{ $testimonial->position }}@if($testimonial->company), {{ $testimonial->company }}@endif</p>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-500">No testimonials available at the moment.</p>
                </div>
                @endforelse
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="py-20 bg-white">
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Impact in Numbers</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Trusted by hundreds of clients to make their dreams come true.
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center group" x-data="{ count: 0, target: 500, inView: false }" x-intersect="inView = true" x-effect="if (inView && count < target) { let interval = setInterval(() => { count += 10; if (count >= target) { count = target; clearInterval(interval); } }, 50); }">
                    <div class="bg-gradient-to-br from-blue-100 to-blue-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg class="h-10 w-10 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="text-4xl font-bold text-blue-600 mb-2" x-text="count + '+'"></div>
                    <div class="text-gray-600 font-medium">Happy Clients</div>
                </div>

                <div class="text-center group" x-data="{ count: 0, target: 50, inView: false }" x-intersect="inView = true" x-effect="if (inView && count < target) { let interval = setInterval(() => { count += 1; if (count >= target) { count = target; clearInterval(interval); } }, 100); }">
                    <div class="bg-gradient-to-br from-green-100 to-green-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg class="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="text-4xl font-bold text-green-600 mb-2" x-text="count + '+'"></div>
                    <div class="text-gray-600 font-medium">Countries Served</div>
                </div>

                <div class="text-center group" x-data="{ count: 0, target: 200, inView: false }" x-intersect="inView = true" x-effect="if (inView && count < target) { let interval = setInterval(() => { count += 5; if (count >= target) { count = target; clearInterval(interval); } }, 50); }">
                    <div class="bg-gradient-to-br from-purple-100 to-purple-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg class="h-10 w-10 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <div class="text-4xl font-bold text-purple-600 mb-2" x-text="count + '+'"></div>
                    <div class="text-gray-600 font-medium">Students Placed</div>
                </div>

                <div class="text-center group" x-data="{ count: 0, target: 98, inView: false }" x-intersect="inView = true" x-effect="if (inView && count < target) { let interval = setInterval(() => { count += 2; if (count >= target) { count = target; clearInterval(interval); } }, 50); }">
                    <div class="bg-gradient-to-br from-yellow-100 to-yellow-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                        <svg class="h-10 w-10 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="text-4xl font-bold text-yellow-600 mb-2" x-text="count + '%'"></div>
                    <div class="text-gray-600 font-medium">Success Rate</div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Journey?</h2>
            <p class="text-xl mb-8 text-blue-100">
                Let us help you achieve your travel and education dreams. Contact us today to get started.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    Contact Us
                </a>
                <a href="{{ route('services') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-700 transform hover:scale-105 transition-all duration-200">
                    View Our Services
                </a>
            </div>
        </div>
    </section>
</x-layout>
