<x-layout title="Our Services - Global Ventures Tanzania">
    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6" x-data="{ show: false }" x-init="setTimeout(() => show = true, 300)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Our Services
            </h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto" x-data="{ show: false }" x-init="setTimeout(() => show = true, 600)" x-show="show" x-transition:enter="transition ease-out duration-1000" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                Comprehensive solutions to help you achieve your travel and education goals.
            </p>
        </div>
    </section>

    @foreach($serviceCategories as $category)
    <!-- {{ $category->name }} -->
    <section class="py-20 {{ $loop->even ? 'bg-gray-50' : 'bg-white' }}">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16" x-data="{ inView: false }" x-intersect="inView = true">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" x-show="inView" x-transition:enter="transition ease-out duration-800" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                    {{ $category->name }}
                </h2>
                @if($category->description)
                <p class="text-xl text-gray-600 max-w-3xl mx-auto" x-show="inView" x-transition:enter="transition ease-out duration-800 delay-200" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                    {{ $category->description }}
                </p>
                @endif
            </div>

            @if($category->services->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{{ min(4, $category->services->count()) }} gap-8">
                @foreach($category->services as $service)
                <div class="bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group border border-gray-100" x-data="{ inView: false }" x-intersect="inView = true">
                    <div x-show="inView" x-transition:enter="transition ease-out duration-800 delay-{{ $loop->index * 100 }}" x-transition:enter-start="opacity-0 transform translate-y-10" x-transition:enter-end="opacity-100 transform translate-y-0">
                        <div class="bg-gradient-to-br {{ $service->gradient_class }} h-32 flex items-center justify-center">
                            @if($service->icon)
                                <i class="{{ $service->icon }} text-4xl text-white"></i>
                            @else
                                <svg class="h-14 w-14 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                            @endif
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-3">{{ $service->title }}</h3>
                            <p class="text-gray-600 mb-4">{{ $service->description }}</p>
                            @if($service->features && count($service->features) > 0)
                            <ul class="text-gray-600 space-y-1 text-sm">
                                @foreach($service->features as $feature)
                                <li>• {{ $feature }}</li>
                                @endforeach
                            </ul>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            @else
            <div class="text-center py-12">
                <p class="text-gray-500">No services available in this category yet.</p>
            </div>
            @endif
        </div>
    </section>
    @endforeach

    <!-- Call to Action -->
    <section class="py-20 bg-gradient-to-br from-blue-600 to-indigo-700 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Journey?</h2>
            <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                Contact us today to discuss your travel, study abroad, or visa needs. Our expert team is here to help you every step of the way.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    Get Started Today
                </a>
                <a href="{{ route('about') }}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-700 transform hover:scale-105 transition-all duration-200">
                    Learn More About Us
                </a>
            </div>
        </div>
    </section>
</x-layout>
