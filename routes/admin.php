<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\ServiceCategoryController;
use App\Http\Controllers\Admin\BlogPostController;
use App\Http\Controllers\Admin\TestimonialController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\FeedbackController;
use App\Http\Controllers\Admin\ContactInquiryController;
use App\Http\Controllers\Admin\ActivityLogController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here are the admin routes for the Global Ventures admin panel.
| All routes are protected by admin middleware and permission checks.
|
*/

Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {

    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Users Management
    Route::middleware('permission:view_users')->group(function () {
        Route::resource('users', UserController::class);
        Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
    });

    // Roles & Permissions Management
    Route::middleware('permission:view_roles')->group(function () {
        Route::resource('roles', RoleController::class);
        Route::post('roles/{role}/permissions', [RoleController::class, 'updatePermissions'])->name('roles.permissions');
    });

    // Service Categories Management
    Route::middleware('permission:view_services')->group(function () {
        Route::resource('service-categories', ServiceCategoryController::class);
        Route::post('service-categories/reorder', [ServiceCategoryController::class, 'reorder'])->name('service-categories.reorder');
    });

    // Services Management
    Route::middleware('permission:view_services')->group(function () {
        Route::resource('services', ServiceController::class);
        Route::post('services/reorder', [ServiceController::class, 'reorder'])->name('services.reorder');
    });

    // Blog Management
    Route::middleware('permission:view_blog')->group(function () {
        Route::resource('blog-posts', BlogPostController::class);
        Route::post('blog-posts/{blogPost}/publish', [BlogPostController::class, 'publish'])->name('blog-posts.publish');
        Route::post('blog-posts/{blogPost}/unpublish', [BlogPostController::class, 'unpublish'])->name('blog-posts.unpublish');
    });

    // Testimonials Management
    Route::middleware('permission:view_testimonials')->group(function () {
        Route::resource('testimonials', TestimonialController::class);
        Route::post('testimonials/reorder', [TestimonialController::class, 'reorder'])->name('testimonials.reorder');
    });

    // FAQ Management
    Route::middleware('permission:view_faqs')->group(function () {
        Route::resource('faqs', FaqController::class);
        Route::post('faqs/reorder', [FaqController::class, 'reorder'])->name('faqs.reorder');
    });

    // Contact Inquiries
    Route::middleware('permission:view_inquiries')->group(function () {
        Route::resource('inquiries', ContactInquiryController::class)->except(['create', 'store']);
        Route::post('inquiries/{inquiry}/assign', [ContactInquiryController::class, 'assign'])->name('inquiries.assign');
        Route::post('inquiries/{inquiry}/respond', [ContactInquiryController::class, 'respond'])->name('inquiries.respond');
        Route::get('inquiries/export/csv', [ContactInquiryController::class, 'exportCsv'])->name('inquiries.export.csv');
    });

    // Feedback Management
    Route::middleware('permission:view_feedback')->group(function () {
        Route::resource('feedback', FeedbackController::class)->except(['create', 'store']);
        Route::post('feedback/{feedback}/status', [FeedbackController::class, 'updateStatus'])->name('feedback.update-status');
        Route::post('feedback/{feedback}/toggle-public', [FeedbackController::class, 'togglePublic'])->name('feedback.toggle-public');
    });

    // Activity Logs
    Route::middleware('permission:view_logs')->group(function () {
        Route::get('activity-logs', [ActivityLogController::class, 'index'])->name('activity-logs.index');
        Route::get('activity-logs/{log}', [ActivityLogController::class, 'show'])->name('activity-logs.show');
    });

    // Settings
    Route::middleware('permission:view_settings')->group(function () {
        Route::get('settings', [SettingController::class, 'index'])->name('settings.index');
        Route::post('settings', [SettingController::class, 'update'])->name('settings.update');
        Route::post('settings/upload-logo', [SettingController::class, 'uploadLogo'])->name('settings.upload-logo');
        Route::post('settings/upload-favicon', [SettingController::class, 'uploadFavicon'])->name('settings.upload-favicon');
        Route::post('settings/clear-cache', [SettingController::class, 'clearCache'])->name('settings.clear-cache');
    });

    // Profile Management
    Route::get('profile', [ProfileController::class, 'index'])->name('profile.index');
    Route::post('profile/update', [ProfileController::class, 'updateProfile'])->name('profile.update');
    Route::post('profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::post('profile/avatar', [ProfileController::class, 'uploadAvatar'])->name('profile.avatar');
    Route::post('profile/preferences', [ProfileController::class, 'updatePreferences'])->name('profile.preferences');
    Route::get('profile/activity', [ProfileController::class, 'activityLogs'])->name('profile.activity');

    // File Upload Routes
    Route::post('upload/image', [DashboardController::class, 'uploadImage'])->name('upload.image');
    Route::post('upload/file', [DashboardController::class, 'uploadFile'])->name('upload.file');

});
