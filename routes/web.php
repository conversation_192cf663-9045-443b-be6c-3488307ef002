<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Frontend\PageController;
use App\Http\Controllers\Api\ServiceCategoryController as ApiServiceCategoryController;
use Illuminate\Support\Facades\Route;

// Include admin routes
require __DIR__.'/admin.php';

// Main website routes
Route::get('/', [PageController::class, 'home'])->name('home');
Route::get('/about', [PageController::class, 'about'])->name('about');
Route::get('/services', [PageController::class, 'services'])->name('services');
Route::get('/contact', [PageController::class, 'contact'])->name('contact');
Route::get('/faq', [PageController::class, 'faq'])->name('faq');

Route::get('/terms', function () {
    return view('pages.terms');
})->name('terms');

// Feedback routes
Route::get('/feedback', [App\Http\Controllers\Frontend\FeedbackController::class, 'create'])->name('feedback.create');
Route::post('/feedback', [App\Http\Controllers\Frontend\FeedbackController::class, 'store'])->name('feedback.store');
Route::get('/testimonials', [App\Http\Controllers\Frontend\FeedbackController::class, 'public'])->name('feedback.public');

// Blog routes
Route::get('/blog', [PageController::class, 'blogIndex'])->name('blog.index');
Route::get('/blog/{slug}', [PageController::class, 'blogSingle'])->name('blog.single');

Route::get('/dashboard', function () {
    // Redirect admin users to admin dashboard
    if (auth()->user()->role) {
        return redirect()->route('admin.dashboard');
    }
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';

// API Routes
Route::prefix('api')->name('api.')->group(function () {
    Route::apiResource('service-categories', ApiServiceCategoryController::class);
});

// Admin access page
Route::get('/admin-access', function () {
    return view('admin-access');
})->name('admin.access');
